package com.ruoyi.code.debtConversion.domain.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.code.debtConversion.domain.InvoicingBusiness;
import com.ruoyi.code.debtConversion.domain.until.ExcelCheck;
import lombok.Data;


import java.util.Date;
import java.util.List;

import static com.ruoyi.code.debtConversion.domain.until.ExcelCheck.Type.EXPORT;


/**
 * 开票申请业务对象 dc_invoicing_business
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
public class InvoicingBusinessVo extends InvoicingBusiness
{
    private List<InvoicingApplicationVo> invoicingApplicationVoList;
    /*
     完成状态 1.未完成 2.已完成
     */
    private String completionStatus;
    /**
     * 1.业务 2.财务
     */
    private String exportTpye;

    @ExcelCheck(name = "序号" , sort = 1,type= EXPORT)
    private int serialNumber;

    /** 开票主体 */
    @ExcelCheck(name = "开票主体", sort = 8)
    private String mainBodyName;

    private String companyShortName;

    private String companyName;

    private Date startInvoicingApplicationTime;

    private Date endInvoicingApplicationTime;


    private List<String> invoicingApplicationCodes;

    @JSONField(serialize = false, deserialize = false)
    private List<Long> ids;
    /**
     * 批量新增开票数据
     */
    private List<InvoicingBusiness> successList;

    private List<DebtConversionVo> debtConversionVoList;
    /**
     * 借款数据
     */
    private Long fileId;

    @JsonFormat(pattern = "HH:mm")
    private Date invoicingApplicationTimeHHmm;

    private String weekday;


    @JsonFormat(pattern = "HH:mm")
    private Date pushTimeHHmm;

    private String pushWeekday;

    private List<Long> authorityCompanyIds;

    private String dataSources;
}

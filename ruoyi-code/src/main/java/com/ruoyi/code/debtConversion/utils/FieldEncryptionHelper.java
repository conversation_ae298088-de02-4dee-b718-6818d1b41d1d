package com.ruoyi.code.debtConversion.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字段加解密辅助类
 * 用于处理实体对象中带有@AESEncrypted注解的字段
 *
 * <AUTHOR>
 * @date 2024
 */
public class FieldEncryptionHelper {

    private static final Logger logger = LoggerFactory.getLogger(FieldEncryptionHelper.class);

    /**
     * 缓存类的加密字段信息，避免重复反射
     */
    private static final Map<Class<?>, List<EncryptedFieldInfo>> ENCRYPTED_FIELDS_CACHE = new ConcurrentHashMap<>();

    /**
     * 加密字段信息
     */
    private static class EncryptedFieldInfo {
        private Field field;
        private AESEncrypted annotation;
        private Field fuzzySearchField;

        public EncryptedFieldInfo(Field field, AESEncrypted annotation) {
            this.field = field;
            this.annotation = annotation;
            this.field.setAccessible(true);
        }

        public void setFuzzySearchField(Field fuzzySearchField) {
            this.fuzzySearchField = fuzzySearchField;
            if (this.fuzzySearchField != null) {
                this.fuzzySearchField.setAccessible(true);
            }
        }
    }

    /**
     * 加密对象中的敏感字段
     *
     * @param obj 待加密的对象
     * @param <T> 对象类型
     * @return 加密后的对象
     */
    public static <T> T encryptFields(T obj) {
        if (obj == null) {
            return null;
        }

        List<EncryptedFieldInfo> encryptedFields = getEncryptedFields(obj.getClass());

        for (EncryptedFieldInfo fieldInfo : encryptedFields) {
            try {
                Object value = fieldInfo.field.get(obj);
                if (value instanceof String && StringUtils.hasText((String) value)) {
                    String plainText = (String) value;

                    // 检查字段是否已经被加密，避免重复加密
                    if (isFieldAlreadyEncrypted(plainText, fieldInfo.annotation.format())) {
                        if (logger.isDebugEnabled()) {
                            logger.debug("字段 {} 已经是加密状态，跳过加密", fieldInfo.field.getName());
                        }
                        continue;
                    }

                    // 加密主字段，使用指定的格式
                    String encryptedValue = AESEncryptionUtils.encrypt(plainText, fieldInfo.annotation.key(), fieldInfo.annotation.format());

                    // 如果是HEX格式，去掉前缀只存储纯HEX数据到字段中
                    String fieldValue = encryptedValue;
                    if (fieldInfo.annotation.format() == AESEncrypted.EncryptionFormat.HEX &&
                        encryptedValue != null && encryptedValue.startsWith("HEX:")) {
                        fieldValue = encryptedValue.substring(4); // 去掉"HEX:"前缀
                    }

                    fieldInfo.field.set(obj, fieldValue);

                    // 处理模糊搜索字段
                    if (fieldInfo.annotation.enableFuzzySearch() && fieldInfo.fuzzySearchField != null) {
                        String fuzzySearchText = AESEncryptionUtils.generateFuzzySearchText(plainText);
                        fieldInfo.fuzzySearchField.set(obj, fuzzySearchText);
                    }

                    if (logger.isDebugEnabled()) {
                        logger.debug("字段 {} 已加密", fieldInfo.field.getName());
                    }
                }
            } catch (Exception e) {
                logger.error("加密字段 {} 失败: {}", fieldInfo.field.getName(), e.getMessage(), e);
            }
        }

        return obj;
    }

    /**
     * 解密对象中的敏感字段
     *
     * @param obj 待解密的对象
     * @param <T> 对象类型
     * @return 解密后的对象
     */
    public static <T> T decryptFields(T obj) {
        if (obj == null) {
            return null;
        }

        List<EncryptedFieldInfo> encryptedFields = getEncryptedFields(obj.getClass());

        for (EncryptedFieldInfo fieldInfo : encryptedFields) {
            try {
                Object value = fieldInfo.field.get(obj);
                if (value instanceof String && StringUtils.hasText((String) value)) {
                    String encryptedValue = (String) value;

                    // 检查字段是否已经被解密，避免重复解密
                    if (!isFieldAlreadyEncrypted(encryptedValue, fieldInfo.annotation.format())) {
                        if (logger.isDebugEnabled()) {
                            logger.debug("字段 {} 已经是明文状态，跳过解密", fieldInfo.field.getName());
                        }
                        continue;
                    }

                    // 如果是HEX格式且字段值是纯HEX字符串，需要添加前缀
                    String valueToDecrypt = encryptedValue;
                    if (fieldInfo.annotation.format() == AESEncrypted.EncryptionFormat.HEX &&
                        !AESEncryptionUtils.isEncrypted(encryptedValue) &&
                        MySQLHexUtils.isValidHexString(encryptedValue)) {
                        valueToDecrypt = "HEX:" + encryptedValue;
                    }

                    String decryptedValue = AESEncryptionUtils.decrypt(valueToDecrypt, fieldInfo.annotation.key());
                    fieldInfo.field.set(obj, decryptedValue);

                    if (logger.isDebugEnabled()) {
                        logger.debug("字段 {} 已解密", fieldInfo.field.getName());
                    }
                }
            } catch (Exception e) {
                logger.error("解密字段 {} 失败: {}", fieldInfo.field.getName(), e.getMessage(), e);
            }
        }

        return obj;
    }

    /**
     * 批量加密对象列表
     *
     * @param list 对象列表
     * @param <T> 对象类型
     * @return 加密后的对象列表
     */
    public static <T> List<T> encryptFieldsList(List<T> list) {
        if (list == null || list.isEmpty()) {
            return list;
        }

        for (T obj : list) {
            encryptFields(obj);
        }

        return list;
    }

    /**
     * 批量解密对象列表
     *
     * @param list 对象列表
     * @param <T> 对象类型
     * @return 解密后的对象列表
     */
    public static <T> List<T> decryptFieldsList(List<T> list) {
        if (list == null || list.isEmpty()) {
            return list;
        }

        for (T obj : list) {
            decryptFields(obj);
        }

        return list;
    }

    /**
     * 获取类中的加密字段信息
     *
     * @param clazz 类
     * @return 加密字段信息列表
     */
    private static List<EncryptedFieldInfo> getEncryptedFields(Class<?> clazz) {
        return ENCRYPTED_FIELDS_CACHE.computeIfAbsent(clazz, k -> {
            List<EncryptedFieldInfo> encryptedFields = new ArrayList<>();

            // 遍历所有字段，包括父类字段
            Class<?> currentClass = clazz;
            while (currentClass != null && currentClass != Object.class) {
                Field[] fields = currentClass.getDeclaredFields();

                for (Field field : fields) {
                    AESEncrypted annotation = field.getAnnotation(AESEncrypted.class);
                    if (annotation != null && field.getType() == String.class) {
                        // 设置字段可访问
                        field.setAccessible(true);

                        EncryptedFieldInfo fieldInfo = new EncryptedFieldInfo(field, annotation);

                        // 查找对应的模糊搜索字段
                        if (annotation.enableFuzzySearch()) {
                            String fuzzyFieldName = field.getName() + annotation.fuzzySearchSuffix();
                            Field fuzzyField = findField(currentClass, fuzzyFieldName);
                            if (fuzzyField != null) {
                                fuzzyField.setAccessible(true);
                            }
                            fieldInfo.setFuzzySearchField(fuzzyField);
                        }

                        encryptedFields.add(fieldInfo);

                        if (logger.isDebugEnabled()) {
                            logger.debug("找到加密字段: {}.{} - key: {}, format: {}",
                                       currentClass.getSimpleName(), field.getName(),
                                       annotation.key(), annotation.format());
                        }
                    }
                }

                currentClass = currentClass.getSuperclass();
            }

            return encryptedFields;
        });
    }

    /**
     * 查找指定名称的字段
     *
     * @param clazz 类
     * @param fieldName 字段名
     * @return 字段对象，如果未找到返回null
     */
    private static Field findField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 检查字段是否已经被加密
     *
     * @param fieldValue 字段值
     * @param format 加密格式
     * @return 是否已加密
     */
    private static boolean isFieldAlreadyEncrypted(String fieldValue, AESEncrypted.EncryptionFormat format) {
        if (!StringUtils.hasText(fieldValue)) {
            return false;
        }

        switch (format) {
            case HEX:
                // HEX格式：需要更严格的验证，避免将身份证号等数字字符串误判为加密数据
                return isValidEncryptedHexString(fieldValue);
            case BASE64:
                // BASE64格式：检查是否有ENC:前缀
                return fieldValue.startsWith("ENC:");
            case BINARY:
                // BINARY格式：检查是否有BIN:前缀
                return fieldValue.startsWith("BIN:");
            default:
                return false;
        }
    }

    /**
     * 验证是否为有效的加密HEX字符串
     * 与普通的HEX验证不同，这里需要排除身份证号、手机号等可能被误判的数据
     *
     * @param hexString 待验证的字符串
     * @return 是否为有效的加密HEX字符串
     */
    private static boolean isValidEncryptedHexString(String hexString) {
        if (!StringUtils.hasText(hexString)) {
            return false;
        }

        // 如果有HEX:前缀，说明是明确的加密数据
        if (hexString.startsWith("HEX:")) {
            return true;
        }

        // 基本的HEX格式验证
        if (!MySQLHexUtils.isValidHexString(hexString)) {
            return false;
        }

        // AES加密后的数据长度检查：AES-128的块大小是16字节，HEX编码后是32字符
        // 由于PKCS5Padding，加密后的长度应该是32的倍数
        if (hexString.length() % 32 != 0) {
            return false;
        }

        // 长度检查：加密后的数据通常比较长，至少32字符（16字节）
        if (hexString.length() < 32) {
            return false;
        }

        // 排除常见的非加密数据模式
        // 身份证号：18位数字，可能包含X
        if (hexString.length() == 18 && hexString.matches("^[0-9]{17}[0-9Xx]$")) {
            return false;
        }

        // 手机号：11位数字
        if (hexString.length() == 11 && hexString.matches("^[0-9]{11}$")) {
            return false;
        }

        // 如果字符串只包含数字，且长度不符合加密数据特征，很可能不是加密数据
        if (hexString.matches("^[0-9]+$") && hexString.length() < 32) {
            return false;
        }

        return true;
    }

    /**
     * 清除缓存
     */
    public static void clearCache() {
        ENCRYPTED_FIELDS_CACHE.clear();
    }
}

package com.ruoyi.code.debtConversion.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Base64;
import java.util.regex.Pattern;

/**
 * AES加解密工具类
 * 基于项目现有的AESUtils进行增强，专门用于字段加解密
 *
 * <AUTHOR>
 * @date 2024
 */
public class AESEncryptionUtils {

    private static final Logger logger = LoggerFactory.getLogger(AESEncryptionUtils.class);

    /**
     * 默认加密密钥
     */
    private static final String DEFAULT_KEY = "MySecretKey12345";

    /**
     * 加密算法
     */
    private static final String ALGORITHM = "AES";

    /**
     * 加密模式
     */
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";

    /**
     * 字符编码
     */
    private static final String CHARSET = "UTF-8";

    /**
     * 加密数据前缀，用于标识加密数据
     */
    private static final String ENCRYPTED_PREFIX = "ENC:";

    /**
     * HEX格式加密数据前缀
     */
    private static final String HEX_ENCRYPTED_PREFIX = "HEX:";

    /**
     * 二进制格式加密数据前缀
     */
    private static final String BINARY_ENCRYPTED_PREFIX = "BIN:";

    /**
     * 判断字符串是否为加密数据
     */
    private static final Pattern ENCRYPTED_PATTERN = Pattern.compile("^(ENC:|HEX:|BIN:).+");

    /**
     * 加密字符串（使用默认Base64格式）
     *
     * @param plainText 明文
     * @return 加密后的字符串，带有ENC:前缀
     */
    public static String encrypt(String plainText) {
        return encrypt(plainText, DEFAULT_KEY, AESEncrypted.EncryptionFormat.BASE64);
    }

    /**
     * 加密字符串（使用默认Base64格式）
     *
     * @param plainText 明文
     * @param key 加密密钥
     * @return 加密后的字符串，带有ENC:前缀
     */
    public static String encrypt(String plainText, String key) {
        return encrypt(plainText, key, AESEncrypted.EncryptionFormat.BASE64);
    }

    /**
     * 加密字符串（指定格式）
     *
     * @param plainText 明文
     * @param key 加密密钥
     * @param format 加密格式
     * @return 加密后的字符串，带有相应前缀
     */
    public static String encrypt(String plainText, String key, AESEncrypted.EncryptionFormat format) {
        if (!StringUtils.hasText(plainText)) {
            return plainText;
        }

        // 如果已经是加密数据，直接返回
        if (isEncrypted(plainText)) {
            return plainText;
        }

        try {
            String actualKey = StringUtils.hasText(key) ? key : DEFAULT_KEY;
            byte[] encryptedBytes = encryptToBytes(plainText, actualKey);

            switch (format) {
                case HEX:
                    return HEX_ENCRYPTED_PREFIX + bytesToHex(encryptedBytes);
                case BINARY:
                    return BINARY_ENCRYPTED_PREFIX + Base64.getEncoder().encodeToString(encryptedBytes);
                case BASE64:
                default:
                    return ENCRYPTED_PREFIX + Base64.getEncoder().encodeToString(encryptedBytes);
            }
        } catch (Exception e) {
            logger.error("加密失败: {}", e.getMessage(), e);
            return plainText;
        }
    }

    /**
     * 解密字符串
     *
     * @param encryptedText 加密文本
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedText) {
        return decrypt(encryptedText, DEFAULT_KEY);
    }

    /**
     * 解密字符串
     *
     * @param encryptedText 加密文本
     * @param key 解密密钥
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedText, String key) {
        if (!StringUtils.hasText(encryptedText)) {
            return encryptedText;
        }

        // 如果不是加密数据，直接返回
        if (!isEncrypted(encryptedText)) {
            return encryptedText;
        }

        try {
            String actualKey = StringUtils.hasText(key) ? key : DEFAULT_KEY;
            byte[] encryptedBytes;

            // 根据前缀判断加密格式
            if (encryptedText.startsWith(HEX_ENCRYPTED_PREFIX)) {
                String hexData = encryptedText.substring(HEX_ENCRYPTED_PREFIX.length());
                encryptedBytes = hexToBytes(hexData);
            } else if (encryptedText.startsWith(BINARY_ENCRYPTED_PREFIX)) {
                String base64Data = encryptedText.substring(BINARY_ENCRYPTED_PREFIX.length());
                encryptedBytes = Base64.getDecoder().decode(base64Data);
            } else if (encryptedText.startsWith(ENCRYPTED_PREFIX)) {
                String base64Data = encryptedText.substring(ENCRYPTED_PREFIX.length());
                encryptedBytes = Base64.getDecoder().decode(base64Data);
            } else {
                // 兼容旧格式
                return AESUtils.decrypt(encryptedText, createKey(actualKey), TRANSFORMATION, CHARSET);
            }

            return decryptFromBytes(encryptedBytes, actualKey);
        } catch (Exception e) {
            logger.error("解密失败: {}", e.getMessage(), e);
            return encryptedText;
        }
    }

    /**
     * 判断字符串是否为加密数据
     *
     * @param text 待判断的字符串
     * @return 是否为加密数据
     */
    public static boolean isEncrypted(String text) {
        return StringUtils.hasText(text) && ENCRYPTED_PATTERN.matcher(text).matches();
    }

    /**
     * 获取加密数据的格式
     *
     * @param encryptedText 加密文本
     * @return 加密格式
     */
    public static AESEncrypted.EncryptionFormat getEncryptionFormat(String encryptedText) {
        if (!StringUtils.hasText(encryptedText)) {
            return null;
        }

        if (encryptedText.startsWith(HEX_ENCRYPTED_PREFIX)) {
            return AESEncrypted.EncryptionFormat.HEX;
        } else if (encryptedText.startsWith(BINARY_ENCRYPTED_PREFIX)) {
            return AESEncrypted.EncryptionFormat.BINARY;
        } else if (encryptedText.startsWith(ENCRYPTED_PREFIX)) {
            return AESEncrypted.EncryptionFormat.BASE64;
        }

        return null;
    }

    /**
     * 生成用于模糊搜索的字符串
     * 将明文转换为可用于模糊搜索的格式
     *
     * @param plainText 明文
     * @return 用于模糊搜索的字符串
     */
    public static String generateFuzzySearchText(String plainText) {
        if (!StringUtils.hasText(plainText)) {
            return plainText;
        }

        // 移除特殊字符，转换为小写，便于模糊搜索
        return plainText.toLowerCase()
                .replaceAll("[\\s\\-_\\.]", "")
                .replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "");
    }

    /**
     * 创建密钥对象
     *
     * @param key 密钥字符串
     * @return 密钥对象
     */
    private static Key createKey(String key) {
        // 确保密钥长度为16字节（128位）
        byte[] keyBytes = new byte[16];
        byte[] sourceBytes = key.getBytes(StandardCharsets.UTF_8);
        System.arraycopy(sourceBytes, 0, keyBytes, 0, Math.min(sourceBytes.length, keyBytes.length));
        return new SecretKeySpec(keyBytes, ALGORITHM);
    }

    /**
     * 批量加密处理
     *
     * @param texts 待加密的字符串数组
     * @return 加密后的字符串数组
     */
    public static String[] batchEncrypt(String... texts) {
        if (texts == null) {
            return null;
        }

        String[] result = new String[texts.length];
        for (int i = 0; i < texts.length; i++) {
            result[i] = encrypt(texts[i]);
        }
        return result;
    }

    /**
     * 批量解密处理
     *
     * @param encryptedTexts 待解密的字符串数组
     * @return 解密后的字符串数组
     */
    public static String[] batchDecrypt(String... encryptedTexts) {
        if (encryptedTexts == null) {
            return null;
        }

        String[] result = new String[encryptedTexts.length];
        for (int i = 0; i < encryptedTexts.length; i++) {
            result[i] = decrypt(encryptedTexts[i]);
        }
        return result;
    }

    /**
     * 加密为字节数组
     *
     * @param plainText 明文
     * @param key 密钥
     * @return 加密后的字节数组
     * @throws Exception 加密异常
     */
    private static byte[] encryptToBytes(String plainText, String key) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, createKey(key));
        return cipher.doFinal(plainText.getBytes(CHARSET));
    }

    /**
     * 从字节数组解密
     *
     * @param encryptedBytes 加密的字节数组
     * @param key 密钥
     * @return 解密后的明文
     * @throws Exception 解密异常
     */
    private static String decryptFromBytes(byte[] encryptedBytes, String key) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, createKey(key));
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, CHARSET);
    }

    /**
     * 字节数组转HEX字符串
     *
     * @param bytes 字节数组
     * @return HEX字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X", b));
        }
        return result.toString();
    }

    /**
     * HEX字符串转字节数组
     *
     * @param hex HEX字符串
     * @return 字节数组
     */
    private static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
}

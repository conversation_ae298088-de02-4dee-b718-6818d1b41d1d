package com.ruoyi.code.debtConversion.service;

import com.ruoyi.code.debtConversion.domain.SysCompany;
import com.ruoyi.code.debtConversion.domain.vo.SysCompanyVo;

import java.util.List;
import java.util.Set;

/**
 * 全量公司信息Service接口
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
public interface ISysCompanyService
{


    /**
     * 查询全量公司信息列表
     *
     * @param sysCompany 全量公司信息
     * @return 全量公司信息集合
     */
    public List<SysCompanyVo> selectSysCompanyList(SysCompanyVo sysCompany);


    public List<SysCompanyVo> selectCompanyListByCompanyShortNames(Set<String> companyNames);

    public SysCompanyVo selectSysCompanyById(Long id);

    public SysCompany selectSysCompanyByCompanyShortName(SysCompany sysCompany);
}

package com.ruoyi.code.debtConversion.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 开票申请文件对象 dc_invoicing_application_file
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
public class InvoicingApplicationFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 开票主体 */
    @Excel(name = "开票主体")
    private Long mainBodyId;

    /** 开票主题 */
    @Excel(name = "开票主题")
    private String invoicingTheme;

    /**开票状态(1.未开票 2.已开票)*/
    private String invoiceStatus;

}

package com.ruoyi.code.debtConversion.domain.vo;

import com.ruoyi.code.debtConversion.domain.InvoicingApplication;
import com.ruoyi.code.debtConversion.domain.InvoicingApplicationFile;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 开票申请文件对象 dc_invoicing_application_file
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
public class InvoicingApplicationFileVo extends InvoicingApplicationFile
{
    private List<InvoicingApplication> successList;

    private String mainBodyName;

    private String createByName;

    private Date startCreateTime;

    private Date endCreateTime;

    private List<Long> authorityCompanyIds;

    private String dataSources;
}

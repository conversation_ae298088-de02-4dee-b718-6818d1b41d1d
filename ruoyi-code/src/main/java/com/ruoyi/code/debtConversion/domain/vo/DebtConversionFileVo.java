package com.ruoyi.code.debtConversion.domain.vo;

import com.ruoyi.code.debtConversion.domain.DebtConversion;
import com.ruoyi.code.debtConversion.domain.DebtConversionFile;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DebtConversionFileVo extends DebtConversionFile {

    private String companyShortName;

    private Date startCreateTime;

    private Date endCreateTime;

    private String  createByName;

    private List<DebtConversion> successList;
    /**
     * 子表数量
     */
    private Long conversionCount;

    private List<Long> authorityCompanyIds;

    private String dataSources;
}

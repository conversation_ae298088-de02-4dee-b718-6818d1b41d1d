package com.ruoyi.code.debtConversion.service.impl;

import com.ruoyi.code.debtConversion.domain.DimBillNoInfo;
import com.ruoyi.code.debtConversion.domain.vo.DimBillNoInfoMini;
import com.ruoyi.code.debtConversion.mapper.DimBillNoInfoMapper;
import com.ruoyi.code.debtConversion.service.IDimBillNoInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 借据信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Service
public class DimBillNoInfoServiceImpl implements IDimBillNoInfoService
{
    @Autowired
    private DimBillNoInfoMapper dimBillNoInfoMapper;

    /**
     * 查询借据信息
     *
     * @param billAppNo 借据信息主键
     * @return 借据信息
     */
    @Override
    public DimBillNoInfo selectDimBillNoInfoByBillAppNo(String billAppNo)
    {
        return dimBillNoInfoMapper.selectDimBillNoInfoByBillAppNo(billAppNo);
    }

    /**
     * 查询借据信息列表
     *
     * @param dimBillNoInfo 借据信息
     * @return 借据信息
     */
    @Override
    public List<DimBillNoInfo> selectDimBillNoInfoList(DimBillNoInfo dimBillNoInfo)
    {
        return dimBillNoInfoMapper.selectDimBillNoInfoList(dimBillNoInfo);
    }

    /**
     * 新增借据信息
     *
     * @param dimBillNoInfo 借据信息
     * @return 结果
     */
    @Override
    public int insertDimBillNoInfo(DimBillNoInfo dimBillNoInfo)
    {
        return dimBillNoInfoMapper.insertDimBillNoInfo(dimBillNoInfo);
    }

    /**
     * 修改借据信息
     *
     * @param dimBillNoInfo 借据信息
     * @return 结果
     */
    @Override
    public int updateDimBillNoInfo(DimBillNoInfo dimBillNoInfo)
    {
        return dimBillNoInfoMapper.updateDimBillNoInfo(dimBillNoInfo);
    }

    /**
     * 批量删除借据信息
     *
     * @param billAppNos 需要删除的借据信息主键
     * @return 结果
     */
    @Override
    public int deleteDimBillNoInfoByBillAppNos(String[] billAppNos)
    {
        return dimBillNoInfoMapper.deleteDimBillNoInfoByBillAppNos(billAppNos);
    }

    /**
     * 删除借据信息信息
     *
     * @param billAppNo 借据信息主键
     * @return 结果
     */
    @Override
    public int deleteDimBillNoInfoByBillAppNo(String billAppNo)
    {
        return dimBillNoInfoMapper.deleteDimBillNoInfoByBillAppNo(billAppNo);
    }

    /**
     * 小程序 查询借据信息列表
     *
     * @param dimBillNoInfo 借据信息
     * @return 借据信息集合
     */
    @Override
    public List<DimBillNoInfoMini> miniList(DimBillNoInfo dimBillNoInfo){
        return dimBillNoInfoMapper.miniList(dimBillNoInfo);
    }

    @Override
    public int countByIdCardAndPhone(DimBillNoInfo dimBillNoInfo){
        return dimBillNoInfoMapper.countByIdCardAndPhone(dimBillNoInfo);
    }

}

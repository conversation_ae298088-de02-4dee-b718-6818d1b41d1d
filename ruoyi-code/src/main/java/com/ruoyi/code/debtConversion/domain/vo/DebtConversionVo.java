package com.ruoyi.code.debtConversion.domain.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.code.debtConversion.domain.DebtConversion;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 债转通知明细对象 dc_debt_conversion
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
public class DebtConversionVo extends DebtConversion
{
    /** 担保公司 */
    @Excel(name = "担保公司" ,sort = 9)
    private String custName;

    /** 资产方 */
    @Excel(name = "资产方",sort = 8)
    private String partnerName;

    /** 资金方 */
    @Excel(name = "资金方",sort = 7)
    private String fundName;

    /** 债权接收方 */
    @Excel(name = "债权接收方" , sort = 10)
    private String debtRecipientName;

    /** 担保公司 */
    private String custFullName;

    /** 资产方 */
    private String partnerFullName;

    /** 资金方 */
    private String fundFullName;

    /** 债权接收方 */
    private String debtRecipientFullName;

    /** 债转通知完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "债转通知完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss",sort = 11)
    private Date noticeCompleteTime;

    /** 债转通知完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date noticeLaunchTime;
    /**
     * 是否注册小程序
     */
    @Excel(name = "是否注册小程序" ,readConverterExp = "1=否,2=是", sort = 13 )
    private String  registerMiniProgram;

    @JSONField(serialize = false, deserialize = false)
    private List<Long> ids;

    /**
     * 债转通知完成状态
     */
    private String pushStatus;

    private Long invoicingBusinessId;

    private String realIdCard;

    private String realPhoneNum;

    private List<Long> authorityCompanyIds;

    private String dataSources;
}

package com.ruoyi.code.debtConversion.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品信息对象 dim_base_prod_info
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
public class DimBaseProdInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String productNo;

    /** 产品名称 */
    @Excel(name = "产品名称")
    private String productName;

    /** 担保方编号 */
    @Excel(name = "担保方编号")
    private String guarantorNo;

    /** 担保方 */
    @Excel(name = "担保方")
    private String guarantorName;

    /** 平台方编号 */
    @Excel(name = "平台方编号")
    private String platformNo;

    /** 平台方 */
    @Excel(name = "平台方")
    private String platformName;

    /** 资金方编号 */
    @Excel(name = "资金方编号")
    private String financerNo;

    /** 资金方 */
    @Excel(name = "资金方")
    private String financerName;

    /** 项目编号 */
    @Excel(name = "项目编号")
    private String projectNo;

    /** 系统 */
    @Excel(name = "系统")
    private Long systemNo;

    /** 外部系统平台编码 */
    @Excel(name = "外部系统平台编码")
    private String oaPlatformNo;

    /** 担保公司编码 */
    @Excel(name = "担保公司编码")
    private String oaCustNo;

    /** 合作方编码 */
    @Excel(name = "合作方编码")
    private String oaPartnerNo;

    /** 资金方编码 */
    @Excel(name = "资金方编码")
    private String oaFundNo;

    /** 科技方编码 */
    @Excel(name = "科技方编码")
    private String oaItNo;

    /** 项目是否已结束,Y是N否 */
    @Excel(name = "项目是否已结束,Y是N否")
    private String isProjectFinish;

    /** 项目结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "项目结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date projectFinishDate;

    /** 客户是否为企业,Y是C同时含有个人N否 */
    @Excel(name = "客户是否为企业,Y是C同时含有个人N否")
    private String isProjectCompany;

    /** 借据是否通道简版数据,Y是N否 */
    @Excel(name = "借据是否通道简版数据,Y是N否")
    private String isProjectTd;

    /** 借据是否有还款计划数据,Y有N否 */
    @Excel(name = "借据是否有还款计划数据,Y有N否")
    private String isProjectPlan;

    /** 借据是否有还款计划实还更新数据,Y有C有但未上线N否 */
    @Excel(name = "借据是否有还款计划实还更新数据,Y有C有但未上线N否")
    private String isProjectPlanUpdate;

    /** 借据是否有还款计划缩期情况,Y有C有但未上线N否 */
    @Excel(name = "借据是否有还款计划缩期情况,Y有C有但未上线N否")
    private String isProjectPlanReset;

    /** 借据是否有正常还款数据,Y有C有但未上线N否 */
    @Excel(name = "借据是否有正常还款数据,Y有C有但未上线N否")
    private String isProjectRepay1;

    /** 借据是否有提前还款数据,Y有C有但未上线N否 */
    @Excel(name = "借据是否有提前还款数据,Y有C有但未上线N否")
    private String isProjectRepay4;

    /** 借据是否有提前结清数据,Y有C有但未上线N否 */
    @Excel(name = "借据是否有提前结清数据,Y有C有但未上线N否")
    private String isProjectRepay5;

    /** 借据是否有代偿还款数据,Y有C有但未上线N否 */
    @Excel(name = "借据是否有代偿还款数据,Y有C有但未上线N否")
    private String isProjectRepay7;

    /** 借据是否有累计代偿还款数据,Y有C有但未上线N否 */
    @Excel(name = "借据是否有累计代偿还款数据,Y有C有但未上线N否")
    private String isProjectTotalRepay7;

    /** 借据是否有用户累计还款数据,Y有C有但未上线N否 */
    @Excel(name = "借据是否有用户累计还款数据,Y有C有但未上线N否")
    private String isProjectTotalRepay;

    /** 借据是否有追偿还款数据,Y有C有但未上线N否 */
    @Excel(name = "借据是否有追偿还款数据,Y有C有但未上线N否")
    private String isProjectRepay8;

    /** 借据是否代偿时结清,Y是N否 */
    @Excel(name = "借据是否代偿时结清,Y是N否")
    private String isProjectRepay7Finish;

    /** 借据追偿还款是否按正常还款提供,Y是N否 */
    @Excel(name = "借据追偿还款是否按正常还款提供,Y是N否")
    private String isProjectRepay8Normal;

    /** FPD10是否可用，Y是N否 */
    @Excel(name = "FPD10是否可用，Y是N否")
    private String isResultFpd10;

    /** vintage是否可用，Y是N否 */
    @Excel(name = "vintage是否可用，Y是N否")
    private String isResultVintage;

    /** 余额分布是否可用，Y是N否 */
    @Excel(name = "余额分布是否可用，Y是N否")
    private String isResultBalanceDistribution;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 审核状态 0新增审核状态，1修改项目审核中，2删除项目审核中，3（-） */
    @Excel(name = "审核状态 0新增审核状态，1修改项目审核中，2删除项目审核中，3", readConverterExp = "-=")
    private String checkStatus;

    /** 说明 */
    @Excel(name = "说明")
    private String description;

    /** 业务类型 01-个人业务 02-对公业务 */
    @Excel(name = "业务类型 01-个人业务 02-对公业务")
    private String bizType;

    /** 产品类型 11-个人住房商业贷款 12-个人商用房（含商住两用）贷款 13-个人住房公积金贷款 21-个人汽车消费贷款 41-个人经营性贷款 42-个人创业担保贷款 51-农户贷款 52-经营性农户贷款 53-消费性农户贷款 91-其他个人消费贷款 99-其他贷款 */
    @Excel(name = "产品类型 11-个人住房商业贷款 12-个人商用房", readConverterExp = "含=商住两用")
    private String productType;

    /** 最低授信额度 */
    @Excel(name = "最低授信额度")
    private Long minCreditLimit;

    /** 最高授信额度 */
    @Excel(name = "最高授信额度")
    private Long maxCreditLimit;

    /** 最低利率 年化小数 */
    @Excel(name = "最低利率 年化小数")
    private BigDecimal minInteRate;

    /** 最高利率  年化小数 */
    @Excel(name = "最高利率  年化小数")
    private BigDecimal maxInteRate;

    /** 最短授信期限 */
    @Excel(name = "最短授信期限")
    private Long minCreditSpread;

    /** 最长授信期限 */
    @Excel(name = "最长授信期限")
    private Long maxCreditSpread;

    /** 担保方式 01-信用 02-抵押 03-质押 */
    @Excel(name = "担保方式 01-信用 02-抵押 03-质押")
    private String grtMethod;

    /** 还款方式 11-分期等额本息 12-分期等额本金 13-到期还本分期结息 21-到期一次还本付息 90-不区分还款方式 */
    @Excel(name = "还款方式 11-分期等额本息 12-分期等额本金 13-到期还本分期结息 21-到期一次还本付息 90-不区分还款方式")
    private String repaymentMethod;

    /** 是否允许提前还款 0-可以提前还款 1-不可以提前还款 */
    @Excel(name = "是否允许提前还款 0-可以提前还款 1-不可以提前还款")
    private String prepayment;

    /** 还款数据优先级 1-优先还款信息 2-优先还款计划 */
    @Excel(name = "还款数据优先级 1-优先还款信息 2-优先还款计划")
    private Long repaymentData;

    /** 利率（实际年化） */
    @Excel(name = "利率", readConverterExp = "实=际年化")
    private BigDecimal actYearRate;

    /** 前期服务费费率 */
    @Excel(name = "前期服务费费率")
    private BigDecimal serviceRate;

    /** 前期担保费费率 */
    @Excel(name = "前期担保费费率")
    private BigDecimal guaranteeRate;

    /** 前期保证金费率 */
    @Excel(name = "前期保证金费率")
    private BigDecimal marginRate;

    /** 前期代偿金费率 */
    @Excel(name = "前期代偿金费率")
    private BigDecimal compensateRate;

    /** 年化服务费费率 */
    @Excel(name = "年化服务费费率")
    private BigDecimal periodServiceRate;

    /** 年化担保费费率 */
    @Excel(name = "年化担保费费率")
    private BigDecimal periodGuaranteeRate;

    /** 年化保证金费率 */
    @Excel(name = "年化保证金费率")
    private BigDecimal periodMarginRate;

    /** 年化代偿金费率 */
    @Excel(name = "年化代偿金费率")
    private BigDecimal periodCompensateRate;

    /** 罚息日利率 */
    @Excel(name = "罚息日利率")
    private BigDecimal ointRate;

    /** 逾期违约金/滞纳金费率 */
    @Excel(name = "逾期违约金/滞纳金费率")
    private BigDecimal defineRate;

    /** 提前还款违约金费率 */
    @Excel(name = "提前还款违约金费率")
    private BigDecimal advDefineRate;

    /** 代偿天数 */
    @Excel(name = "代偿天数")
    private Long compensateDays;

    /** 代偿规则编号 */
    @Excel(name = "代偿规则编号")
    private String cpstRuleNo;

    /** 宽限期 */
    @Excel(name = "宽限期")
    private Long graceDay;

    /** 免息期 */
    @Excel(name = "免息期")
    private Long interestFreePeriod;

    /** 是否提供代偿 0-是 1-否 */
    @Excel(name = "是否提供代偿 0-是 1-否")
    private String isCpst;

    /** 是否提供追偿 0-是 1-否 */
    @Excel(name = "是否提供追偿 0-是 1-否")
    private String isRecovery;

    /** 费率1 */
    @Excel(name = "费率1")
    private BigDecimal fee1Rate;

    /** 费率2 */
    @Excel(name = "费率2")
    private BigDecimal fee2Rate;

    /** 费率3 */
    @Excel(name = "费率3")
    private BigDecimal fee3Rate;

    /** 费率4 */
    @Excel(name = "费率4")
    private BigDecimal fee4Rate;

    /** 费率5 */
    @Excel(name = "费率5")
    private BigDecimal fee5Rate;

    /** 费率6 */
    @Excel(name = "费率6")
    private BigDecimal fee6Rate;

    /** 费率7 */
    @Excel(name = "费率7")
    private BigDecimal fee7Rate;

    /** 费率8 */
    @Excel(name = "费率8")
    private BigDecimal fee8Rate;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dwCreateTime;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dwUpdateTime;

    /** 是否结束 Y是  N否 */
    @Excel(name = "是否结束 Y是  N否")
    private String isEnd;

}

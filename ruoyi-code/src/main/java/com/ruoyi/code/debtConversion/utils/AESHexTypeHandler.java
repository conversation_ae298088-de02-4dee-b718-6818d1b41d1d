package com.ruoyi.code.debtConversion.utils;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * AES HEX格式类型处理器
 * 专门用于处理MySQL HEX格式的AES加解密
 *
 * <AUTHOR>
 * @date 2024
 */
@MappedTypes(String.class)
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.LONGVARCHAR, JdbcType.BLOB})
public class AESHexTypeHandler extends BaseTypeHandler<String> {

    private static final Logger logger = LoggerFactory.getLogger(AESHexTypeHandler.class);

    /**
     * 设置参数时进行HEX格式加密
     *
     * @param ps 预处理语句
     * @param i 参数索引
     * @param parameter 参数值
     * @param jdbcType JDBC类型
     * @throws SQLException SQL异常
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 使用HEX格式加密数据后存储到数据库
            String hexEncryptedValue = MySQLHexUtils.encryptToHex(parameter);

            // 始终移除HEX:前缀，只存储纯HEX字符串到数据库
            String hexData = hexEncryptedValue;
            if (hexEncryptedValue != null && hexEncryptedValue.startsWith("HEX:")) {
                hexData = hexEncryptedValue.substring(4);
            }

            ps.setString(i, hexData);

            if (logger.isDebugEnabled()) {
                logger.debug("AES HEX加密字段，原值长度: {}, 加密后长度: {}, 存储长度: {}",
                    parameter != null ? parameter.length() : 0,
                    hexEncryptedValue != null ? hexEncryptedValue.length() : 0,
                    hexData != null ? hexData.length() : 0);
            }
        } catch (Exception e) {
            logger.error("AES HEX加密失败，使用原值: {}", e.getMessage());
            ps.setString(i, parameter);
        }
    }

    /**
     * 根据列名获取结果时进行HEX格式解密
     *
     * @param rs 结果集
     * @param columnName 列名
     * @return 解密后的值
     * @throws SQLException SQL异常
     */
    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String hexValue = rs.getString(columnName);
        return decryptHexValue(hexValue);
    }

    /**
     * 根据列索引获取结果时进行HEX格式解密
     *
     * @param rs 结果集
     * @param columnIndex 列索引
     * @return 解密后的值
     * @throws SQLException SQL异常
     */
    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String hexValue = rs.getString(columnIndex);
        return decryptHexValue(hexValue);
    }

    /**
     * 从存储过程获取结果时进行HEX格式解密
     *
     * @param cs 可调用语句
     * @param columnIndex 列索引
     * @return 解密后的值
     * @throws SQLException SQL异常
     */
    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String hexValue = cs.getString(columnIndex);
        return decryptHexValue(hexValue);
    }

    /**
     * 解密HEX值的通用方法
     *
     * @param hexValue HEX格式的加密值（数据库中存储的纯HEX字符串）
     * @return 解密后的值
     */
    private String decryptHexValue(String hexValue) {
        if (hexValue == null) {
            return null;
        }

        try {
            // 数据库中存储的是纯HEX字符串，需要添加前缀后解密
            String hexEncryptedText;
            if (MySQLHexUtils.isValidHexString(hexValue) && !hexValue.startsWith("HEX:")) {
                hexEncryptedText = "HEX:" + hexValue;
            } else {
                hexEncryptedText = hexValue;
            }

            String decryptedValue = MySQLHexUtils.decryptFromHex(hexEncryptedText);

            if (logger.isDebugEnabled()) {
                logger.debug("AES HEX解密字段，数据库HEX值长度: {}, 处理后长度: {}, 解密后长度: {}",
                    hexValue.length(),
                    hexEncryptedText.length(),
                    decryptedValue != null ? decryptedValue.length() : 0);
            }

            return decryptedValue;
        } catch (Exception e) {
            logger.error("AES HEX解密失败，返回原值: {}", e.getMessage());
            return hexValue;
        }
    }
}

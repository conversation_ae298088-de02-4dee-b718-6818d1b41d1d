package com.ruoyi.code.debtConversion.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.code.debtConversion.domain.until.ExcelCheck;
import com.ruoyi.code.debtConversion.utils.AESEncrypted;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 开票申请业务对象 dc_invoicing_business
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
public class InvoicingBusiness extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 开票编号 */
    @ExcelCheck(name = "开票申请编号" ,sort = 2)
    private String invoicingApplicationCode;

    /** 开票时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelCheck(name = "开票申请时间", width = 30, dateFormat = "yyyy-MM-dd" ,sort = 3)
    private Date invoicingApplicationTime;

    /** 渠道
     *  1.对比申请 2.小程序开票 3.导入数据
      */
    //@ExcelCheck(name = "渠道" )
    private String channel;

    /** 借款人()*/
    @ExcelCheck(name = "借款人" ,sort = 4)
    private String borrower;

    /** 手机号 */
    @ExcelCheck(name = "手机号",sort = 5)
    @AESEncrypted(key = "DEBT", enableFuzzySearch = false, format = AESEncrypted.EncryptionFormat.HEX)
    private String phoneNum;

    /** 身份证号 */
    @ExcelCheck(name = "身份证号" ,sort = 6)
    @AESEncrypted(key = "DEBT", enableFuzzySearch = false, format = AESEncrypted.EncryptionFormat.HEX)
    private String idCard;

    /** 开票金额(元) */
    @ExcelCheck(name = "开票金额(元)" ,sort = 7)
    private BigDecimal invoicingAmountTotal;

    /** 文件名称 */
    // @ExcelCheck(name = "文件名称")
    private String fileName;

    /** 文件地址 */
    //@ExcelCheck(name = "文件地址")
    private String fileUrl;

    /** 开票主体 */
    //@ExcelCheck(name = "开票主体")
    private Long mainBodyId;

    /** 接收邮箱 */
    @ExcelCheck(name = "接收邮箱" ,sort = 9)
    private String receivingEmail;

    /** 邮箱推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //@ExcelCheck(name = "邮箱推送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pushTime;

    /**附件删除原因*/
    private String reasonDeleteFile;

    /** 状态
     *  1.开票中 2.开票成功 3.开票失败 4.开票失败(已重新提交)
     */
    private String invoicingStatus;

    /**发票类型 1普通发票-PDF. */
    private String invoicingType;

    /**抬头类型 1个人 */
    private String invoicingHeadType;

    private String reasonFail;
}

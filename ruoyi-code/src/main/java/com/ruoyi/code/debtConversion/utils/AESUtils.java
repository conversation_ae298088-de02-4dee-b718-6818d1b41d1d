package com.ruoyi.code.debtConversion.utils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class AESUtils {
    private static final String KEY_ALGORITHM = "AES";
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";//算法/工作方式/填充方式

    /**
     * 根据密匙长度生成秘钥（经过base64编码）
     *
     * @param keySize
     * @return
     */
    public static String initSecretKey(int keySize) {
        //返回生成指定算法密钥生成器的 KeyGenerator 对象
        KeyGenerator kg = null;
        try {
            kg = KeyGenerator.getInstance(KEY_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalArgumentException("没有找到该算法-->[" + KEY_ALGORITHM + "]");
        }
        //初始化此密钥生成器，使其具有确定的密钥大小
        //AES 要求密钥长度为 128
        kg.init(keySize);
        //生成一个密钥
        SecretKey secretKey = kg.generateKey();
        return Base64.getUrlEncoder().encodeToString(secretKey.getEncoded());//将秘钥转成base64字符串
    }

    /**
     * 恢复秘钥
     *
     * @param key
     * @return
     */
    public static Key toKey(String key) {
        //生成密钥
        return new SecretKeySpec(Base64.getUrlDecoder().decode(key), KEY_ALGORITHM);
    }

//    /**
//     * 加密
//     *
//     * @param data
//     * @param key
//     * @return
//     * @throws Exception
//     */
//    public static String encrypt(String data, Key key, String charSet) throws Exception {
//        return encrypt(data, key, DEFAULT_CIPHER_ALGORITHM, charSet);
//    }
//
//    /**
//     * 加密
//     *
//     * @param data
//     * @param key（经过base64编码）
//     * @return
//     * @throws Exception
//     */
//    public static String encrypt(String data, String key) throws Exception {
//        return encrypt(data, key, DEFAULT_CIPHER_ALGORITHM);
//    }
//
//    /**
//     * 加密
//     *
//     * @param data
//     * @param key             （经过base64编码）
//     * @param cipherAlgorithm
//     * @return
//     * @throws Exception
//     */
//    public static String encrypt(String data, String key, String cipherAlgorithm) throws Exception {
//        //还原密钥
//        Key k = toKey(key);
//        return encrypt(data, k, cipherAlgorithm);
//    }

    /**
     * 加密
     *
     * @param data
     * @param key
     * @param cipherAlgorithm
     * @return
     * @throws Exception
     */
    public static String encrypt(String data, Key key, String cipherAlgorithm, String charSet) throws Exception {
        //实例化
        Cipher cipher = Cipher.getInstance(cipherAlgorithm);
        //使用密钥初始化，设置为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, key);
        //执行操作
        return Base64.getUrlEncoder().encodeToString(cipher.doFinal(data.getBytes(charSet)));
    }
//
//    /**
//     * 解密
//     *
//     * @param data
//     * @param key（经过base64编码）
//     * @return
//     * @throws Exception
//     */
//    public static String decrypt(String data, String key) throws Exception {
//        return decrypt(data, key, DEFAULT_CIPHER_ALGORITHM);
//    }
//
//    /**
//     * 解密
//     *
//     * @param data
//     * @param key
//     * @return
//     * @throws Exception
//     */
//    public static String decrypt(String data, Key key, String charSet) throws Exception {
//        return decrypt(data, key, DEFAULT_CIPHER_ALGORITHM, charSet);
//    }
//
//    /**
//     * 解密
//     *
//     * @param data
//     * @param key（经过base64编码）
//     * @param cipherAlgorithm
//     * @return
//     * @throws Exception
//     */
//    public static String decrypt(String data, String key, String cipherAlgorithm) throws Exception {
//        //还原密钥
//        Key k = toKey(key);
//        return decrypt(data, k, cipherAlgorithm);
//    }

    /**
     * 解密
     *
     * @param data
     * @param key
     * @param cipherAlgorithm
     * @return
     * @throws Exception
     */
    public static String decrypt(String data, Key key, String cipherAlgorithm, String charSet) throws Exception {
        //实例化
        Cipher cipher = Cipher.getInstance(cipherAlgorithm);
        //使用密钥初始化，设置为解密模式
        cipher.init(Cipher.DECRYPT_MODE, key);
        //执行操作
        return new String(cipher.doFinal(Base64.getUrlDecoder().decode(data)), charSet);
    }

    public static String showByteArray(byte[] data) {
        if (null == data) {
            return null;
        }
        StringBuilder sb = new StringBuilder("{");
        for (byte b : data) {
            sb.append(b).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append("}");
        return sb.toString();
    }

}

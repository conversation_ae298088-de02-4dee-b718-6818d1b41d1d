package com.ruoyi.code.debtConversion.service.impl;

import com.ruoyi.code.debtConversion.domain.InvoicingMiddle;
import com.ruoyi.code.debtConversion.mapper.InvoicingMiddleMapper;
import com.ruoyi.code.debtConversion.service.IInvoicingMiddleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 开票申请业务关联数据中间Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
public class InvoicingMiddleServiceImpl implements IInvoicingMiddleService
{
    @Autowired
    private InvoicingMiddleMapper invoicingMiddleMapper;

    /**
     * 查询开票申请业务关联数据中间
     *
     * @param id 开票申请业务关联数据中间主键
     * @return 开票申请业务关联数据中间
     */
    @Override
    public InvoicingMiddle selectInvoicingMiddleById(Long id)
    {
        return invoicingMiddleMapper.selectInvoicingMiddleById(id);
    }

    /**
     * 查询开票申请业务关联数据中间列表
     *
     * @param invoicingMiddle 开票申请业务关联数据中间
     * @return 开票申请业务关联数据中间
     */
    @Override
    public List<InvoicingMiddle> selectInvoicingMiddleList(InvoicingMiddle invoicingMiddle)
    {
        return invoicingMiddleMapper.selectInvoicingMiddleList(invoicingMiddle);
    }

    /**
     * 新增开票申请业务关联数据中间
     *
     * @param invoicingMiddle 开票申请业务关联数据中间
     * @return 结果
     */
    @Override
    public int insertInvoicingMiddle(InvoicingMiddle invoicingMiddle)
    {
        return invoicingMiddleMapper.insertInvoicingMiddle(invoicingMiddle);
    }

    /**
     * 批量新增开票申请业务关联数据中间
     *
     * @param invoicingMiddleList 开票申请业务关联数据中间列表
     * @return 结果
     */
    @Override
    public int batchInsertInvoicingMiddle(List<InvoicingMiddle> invoicingMiddleList)
    {
        return invoicingMiddleMapper.batchInsertInvoicingMiddle(invoicingMiddleList);
    }

    /**
     * 修改开票申请业务关联数据中间
     *
     * @param invoicingMiddle 开票申请业务关联数据中间
     * @return 结果
     */
    @Override
    public int updateInvoicingMiddle(InvoicingMiddle invoicingMiddle)
    {
        return invoicingMiddleMapper.updateInvoicingMiddle(invoicingMiddle);
    }

    /**
     * 批量删除开票申请业务关联数据中间
     *
     * @param ids 需要删除的开票申请业务关联数据中间主键
     * @return 结果
     */
    @Override
    public int deleteInvoicingMiddleByIds(Long[] ids)
    {
        return invoicingMiddleMapper.deleteInvoicingMiddleByIds(ids);
    }

    /**
     * 删除开票申请业务关联数据中间信息
     *
     * @param id 开票申请业务关联数据中间主键
     * @return 结果
     */
    @Override
    public int deleteInvoicingMiddleById(Long id)
    {
        return invoicingMiddleMapper.deleteInvoicingMiddleById(id);
    }
}

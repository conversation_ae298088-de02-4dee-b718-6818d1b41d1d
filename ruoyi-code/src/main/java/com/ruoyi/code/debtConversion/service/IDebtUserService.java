package com.ruoyi.code.debtConversion.service;

import com.ruoyi.code.debtConversion.domain.DebtUser;

import java.util.List;

/**
 * 债转用户Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface IDebtUserService
{
    /**
     * 查询债转用户
     *
     * @param id 债转用户主键
     * @return 债转用户
     */
    public DebtUser selectDebtUserById(Long id);

    /**
     * 查询债转用户列表
     *
     * @param debtUser 债转用户
     * @return 债转用户集合
     */
    public List<DebtUser> selectDebtUserList(DebtUser debtUser);

    public Integer selectDebtUserListCount(DebtUser debtUser);
    /**
     * 新增债转用户
     *
     * @param debtUser 债转用户
     * @return 结果
     */
    public int insertDebtUser(DebtUser debtUser);

    /**
     * 修改债转用户
     *
     * @param debtUser 债转用户
     * @return 结果
     */
    public int updateDebtUser(DebtUser debtUser);

    /**
     * 批量删除债转用户
     *
     * @param ids 需要删除的债转用户主键集合
     * @return 结果
     */
    public int deleteDebtUserByIds(Long[] ids);

    /**
     * 删除债转用户信息
     *
     * @param id 债转用户主键
     * @return 结果
     */
    public int deleteDebtUserById(Long id);

    public DebtUser getInfo(DebtUser debtUser);
}

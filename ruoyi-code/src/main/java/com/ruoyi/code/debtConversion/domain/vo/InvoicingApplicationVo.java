package com.ruoyi.code.debtConversion.domain.vo;

import com.ruoyi.code.debtConversion.domain.InvoicingApplication;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 开票申请明细对象 dc_invoicing_application
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
public class InvoicingApplicationVo extends InvoicingApplication
{
    /** 担保公司 */
    @Excel(name = "担保公司" ,sort = 9)
    private String custName;

    /** 资产方 */
    @Excel(name = "资产方",sort = 8)
    private String partnerName;

    /** 资金方 */
    @Excel(name = "资金方",sort = 7)
    private String fundName;

    private Long custId;
    /**
     * 开票金额
     */
    private BigDecimal amount;

    /**开票主体*/
    private String mainBodyName;
    /** 关联开票中间表 id*/
    private Long invoicingBusinessId;

    private List<String> loanCodeList;

    private boolean invoiceButton;

    /**开票状态(1.未开票 2.已开票)*/
    private String invoiceStatus;

    private Boolean invoicingAmountDiff;

    private Boolean fundNameDiff;

    private Boolean partnerNameDiff;

    private Boolean custNameDiff;

    private Boolean systemFundNameDiff;

    private Boolean systemPartnerNameDiff;

    private Boolean systemCustNameDiff;

    private Boolean loanAmountDiff;

    private Boolean same;

    private String realIdCard;

    private String realPhoneNum;
}

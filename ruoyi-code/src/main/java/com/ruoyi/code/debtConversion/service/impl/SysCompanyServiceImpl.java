package com.ruoyi.code.debtConversion.service.impl;

import com.ruoyi.code.debtConversion.domain.SysCompany;
import com.ruoyi.code.debtConversion.domain.vo.SysCompanyVo;
import com.ruoyi.code.debtConversion.mapper.SysCompanyMapper;
import com.ruoyi.code.debtConversion.service.ISysCompanyService;
import com.ruoyi.common.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 全量公司信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public class SysCompanyServiceImpl implements ISysCompanyService
{
    @Autowired
    private SysCompanyMapper sysCompanyMapper;

    /**
     * 查询全量公司信息列表
     *
     * @param sysCompany 全量公司信息
     * @return 全量公司信息
     */
    @Override
    public List<SysCompanyVo> selectSysCompanyList(SysCompanyVo sysCompany)
    {
        PageUtils.startPage();
        return sysCompanyMapper.selectSysCompanyList(sysCompany);
    }



    @Override
    public List<SysCompanyVo> selectCompanyListByCompanyShortNames(Set<String> companyNames){
        return sysCompanyMapper.selectCompanyListByCompanyShortNames(companyNames);
    }

    @Override
    public SysCompanyVo selectSysCompanyById(Long id)
    {
        return sysCompanyMapper.selectSysCompanyById(id);
    }

    @Override
    public SysCompany selectSysCompanyByCompanyShortName(SysCompany sysCompany){
        return sysCompanyMapper.selectSysCompanyByCompanyShortName(sysCompany);
    }

}

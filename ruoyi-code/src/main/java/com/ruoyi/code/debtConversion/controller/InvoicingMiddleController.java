package com.ruoyi.code.debtConversion.controller;

import com.ruoyi.code.debtConversion.domain.InvoicingMiddle;
import com.ruoyi.code.debtConversion.service.IInvoicingMiddleService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 开票申请业务关联数据中间Controller
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@RestController
@RequestMapping("/invoicing/middle")
public class InvoicingMiddleController extends BaseController
{
    @Autowired
    private IInvoicingMiddleService invoicingMiddleService;

    /**
     * 查询开票申请业务关联数据中间列表
     */
    //@PreAuthorize("@ss.hasPermi('system:middle:list')")
    @GetMapping("/list")
    public TableDataInfo list(InvoicingMiddle invoicingMiddle)
    {
        startPage();
        List<InvoicingMiddle> list = invoicingMiddleService.selectInvoicingMiddleList(invoicingMiddle);
        return getDataTable(list);
    }

    /**
     * 导出开票申请业务关联数据中间列表
     */
    //@PreAuthorize("@ss.hasPermi('system:middle:export')")
    @Log(title = "开票申请业务关联数据中间", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InvoicingMiddle invoicingMiddle)
    {
        List<InvoicingMiddle> list = invoicingMiddleService.selectInvoicingMiddleList(invoicingMiddle);
        ExcelUtil<InvoicingMiddle> util = new ExcelUtil<InvoicingMiddle>(InvoicingMiddle.class);
        util.exportExcel(response, list, "开票申请业务关联数据中间数据");
    }

    /**
     * 获取开票申请业务关联数据中间详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:middle:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(invoicingMiddleService.selectInvoicingMiddleById(id));
    }

    /**
     * 新增开票申请业务关联数据中间
     */
    //@PreAuthorize("@ss.hasPermi('system:middle:add')")
    @Log(title = "开票申请业务关联数据中间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InvoicingMiddle invoicingMiddle)
    {
        return toAjax(invoicingMiddleService.insertInvoicingMiddle(invoicingMiddle));
    }

    /**
     * 修改开票申请业务关联数据中间
     */
    //@PreAuthorize("@ss.hasPermi('system:middle:edit')")
    @Log(title = "开票申请业务关联数据中间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InvoicingMiddle invoicingMiddle)
    {
        return toAjax(invoicingMiddleService.updateInvoicingMiddle(invoicingMiddle));
    }

    /**
     * 删除开票申请业务关联数据中间
     */
    //@PreAuthorize("@ss.hasPermi('system:middle:remove')")
    @Log(title = "开票申请业务关联数据中间", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(invoicingMiddleService.deleteInvoicingMiddleByIds(ids));
    }
}

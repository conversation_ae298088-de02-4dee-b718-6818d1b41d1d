package com.ruoyi.code.debtConversion.mapper;

import com.ruoyi.code.debtConversion.domain.DimBillNoInfo;
import com.ruoyi.code.debtConversion.domain.vo.DimBillNoInfoMini;

import java.util.List;

/**
 * 借据信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public interface DimBillNoInfoMapper
{
    /**
     * 查询借据信息
     *
     * @param billAppNo 借据信息主键
     * @return 借据信息
     */
    public DimBillNoInfo selectDimBillNoInfoByBillAppNo(String billAppNo);

    /**
     * 查询借据信息列表
     *
     * @param dimBillNoInfo 借据信息
     * @return 借据信息集合
     */
    public List<DimBillNoInfo> selectDimBillNoInfoList(DimBillNoInfo dimBillNoInfo);

    /**
     * 新增借据信息
     *
     * @param dimBillNoInfo 借据信息
     * @return 结果
     */
    public int insertDimBillNoInfo(DimBillNoInfo dimBillNoInfo);

    /**
     * 修改借据信息
     *
     * @param dimBillNoInfo 借据信息
     * @return 结果
     */
    public int updateDimBillNoInfo(DimBillNoInfo dimBillNoInfo);

    /**
     * 删除借据信息
     *
     * @param billAppNo 借据信息主键
     * @return 结果
     */
    public int deleteDimBillNoInfoByBillAppNo(String billAppNo);

    /**
     * 批量删除借据信息
     *
     * @param billAppNos 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDimBillNoInfoByBillAppNos(String[] billAppNos);

    /**
     * 小程序 查询借据信息列表
     *
     * @param dimBillNoInfo 借据信息
     * @return 借据信息集合
     */
    public List<DimBillNoInfoMini> miniList(DimBillNoInfo dimBillNoInfo);

    /**
     * 根据身份证号查询数据条数
     *
     * @param dimBillNoInfo 身份证
     * @return 结果
     */
    public int countByIdCardAndPhone(DimBillNoInfo dimBillNoInfo);
}

package com.ruoyi.code.debtConversion.utils;

public class DesensitizationUtils {
    /**
     * 手机号脱敏（保留前3位和后4位）
     * @param phone 手机号码
     * @return 脱敏后的手机号 如：138****5678
     */
    public static String desensitizePhone(String phone) {
        if (phone == null || phone.length() < 11) {
            return phone; // 如果不是11位手机号，返回原值
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    /**
     * 身份证号脱敏（保留前4位和后4位）
     * @param idCard 身份证号
     * @return 脱敏后的身份证号 如：1101********1234
     */
    public static String desensitizeIdCard(String idCard) {
        if (idCard == null || idCard.length() < 9) {
            return idCard; // 如果不足9位，无法保留前后各4位
        }

        // 计算中间需要替换的位数
        int maskLength = idCard.length() - 8;
        StringBuilder masked = new StringBuilder();
        for (int i = 0; i < maskLength; i++) {
            masked.append("*");
        }

        return idCard.substring(0, 4) + masked + idCard.substring(idCard.length() - 4);
    }
}

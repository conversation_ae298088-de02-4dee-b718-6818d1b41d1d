package com.ruoyi.code.debtConversion.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.code.debtConversion.domain.DebtUser;
import com.ruoyi.code.debtConversion.domain.DimBillNoInfo;
import com.ruoyi.code.debtConversion.domain.SmsEqual;
import com.ruoyi.code.debtConversion.domain.SysCompany;
import com.ruoyi.code.debtConversion.domain.vo.SysCompanyVo;
import com.ruoyi.code.debtConversion.service.IDebtUserService;
import com.ruoyi.code.debtConversion.service.IDimBillNoInfoService;
import com.ruoyi.code.debtConversion.service.ISysCompanyService;
import com.ruoyi.code.debtConversion.service.impl.AppTokenService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.http.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 人脸认证控制器
 */
@RestController
@RequestMapping("/face/verify")
public class FaceVerifyController {
//    @Autowired
//    private FaceVerifyService faceVerifyService;
//    @Autowired
//    private IndivAuthUrlService indivAuthUrlService;
    @Autowired
    private IDimBillNoInfoService dimBillNoInfoService;
    @Autowired
    private IDebtUserService debtUserService;
    @Autowired
    private ISysCompanyService companyService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private AppTokenService tokenService;
//    @Value("${esign.project_env}")
//    private String projectEnv;
    @Autowired
    private Environment env;
    @Value("${sms.ip}")
    private String smsIp;
    /**
     * 发起人脸认证
     * @return 包含流程ID和认证URL的Map
     */
//    @PostMapping
//    public Map<String, Object> startFaceVerify(@RequestBody FaceVerify faceVerify) {
//        Map<String, Object> result = new HashMap<>();
//        try {
//            FaceIdentityResponse response = faceVerifyService.startFaceVerify( faceVerify.getName(), faceVerify.getIdNo(), faceVerify.getCallbackUrl());
//            result.put("success", true);
//            result.put("flowId", response.getData().getFlowId());
//            result.put("originalUrl", response.getData().getOriginalUrl());
//            result.put("faceToken", response.getData().getFaceToken());
//            result.put("env",projectEnv);
//            result.put("msg", "人脸认证发起成功");
//        } catch (DefineException e) {
//            //logger.error("人脸认证发起失败", e);
//            result.put("success", false);
////            result.put("code", e.getCode());
//            result.put("msg", e.getMessage());
//        }
//        return result;
//    }

//    /**
//     * 查询人脸认证状态
//     *
//     * @param flowId 流程ID
//     * @return 包含认证状态的Map
//     */
//    @GetMapping("/{flowId}")
//    public Map<String, Object> queryFaceStatus (@PathVariable("flowId")String flowId) {
//        Map<String, Object> result = new HashMap<>();
//        try {
//            QryFaceStatusResponse response = faceVerifyService.queryFaceStatus(flowId);
//            JsonObject jsonObject = new Gson().fromJson(response.getBody(), JsonObject.class);
//            result.put("success", true);
//            result.put("code", 200);
//            result.put("msg", response.getMessage());
//            // 确保 data 存在且是 JsonObject
//            if (jsonObject.has("data") && jsonObject.get("data").isJsonObject()) {
//                JsonObject data = jsonObject.getAsJsonObject("data");
//                // 转换为 Map（兼容性强）
//                Map<String, Object> dataMap = new Gson().fromJson(
//                        data,
//                        new TypeToken<Map<String, Object>>() {}.getType()
//                );
//                result.put("data", dataMap);
//            } else {
//                result.put("data", null); // 或默认值
//            }
//        } catch (DefineException e) {
//            //logger.error("查询人脸认证状态失败", e);
//            result.put("success", false);
////            result.put("code", e.getCode());
//            result.put("msg", e.getMessage());
//        }
//        return result;
//    }

//    /**
//     * 发起人脸认证  https://open.esign.cn/doc/opendoc/identity_service/sg2nty
//     * @return 包含流程ID和认证URL的Map
//     */
//    @GetMapping("/faceUrl")
//    public Map<String, Object> startFaceVerifyV2(FaceVerify faceVerify) {
//        //人脸认证
//        if (!redisCache.exists("debt:face:count" + faceVerify.getIdNo())){
//            redisCache.setCacheObject("debt:face:count" + faceVerify.getIdNo(),1,28800, TimeUnit.SECONDS);
//        } else {
//            int cacheObject = (int) redisCache.getCacheObject("debt:face:count" + faceVerify.getIdNo()) ;
//            if (cacheObject >= 3){
//                throw new RuntimeException ("今日尝试次数过多,请明日再来注册.}");
//            }
//            redisCache.setCacheObject("debt:face:count" + faceVerify.getIdNo() ,cacheObject + 1,28800, TimeUnit.SECONDS);
//        }
//        //请求人脸+短信次数(每日合计不超过三千次)
//        if (!redisCache.exists("debt:api:count")){
//            redisCache.setCacheObject("debt:api:count" ,1,28800, TimeUnit.SECONDS);
//        } else {
//            int cacheObject = (int) redisCache.getCacheObject("debt:api:count") ;
//            if (cacheObject >= 3000){
//                throw new RuntimeException ("今日注册数量过多,请明日再来注册.}");
//            }
//            redisCache.setCacheObject("debt:api:count" ,cacheObject + 1,28800, TimeUnit.SECONDS);
//        }
//
//        Map<String, Object> result = new HashMap<>();
//        try {
//            IndivInfo indivInfo = new IndivInfo();
//            indivInfo.setName(faceVerify.getName());
//            indivInfo.setCertNo(faceVerify.getIdNo());
//            List<String> aat = new ArrayList<>();
//            aat.add("PSN_FACEAUTH_BYURL");
//            ContextInfo contextInfo = new ContextInfo();
//            contextInfo.setRedirectUrl("wechat://back");
//            ConfigParams configParams = new ConfigParams();
//            //不可修改参数(姓名和身份证号)
//            List<String> uneditableInfo = new ArrayList<>();
//            uneditableInfo.add("name");
//            uneditableInfo.add("certNo");
//            configParams.setIndivUneditableInfo(uneditableInfo);
//            IndivAuthUrlResponse response = indivAuthUrlService.getIndivAuthUrl("PSN_FACEAUTH_BYURL", aat, null, contextInfo, indivInfo, configParams);
//            result.put("code", 200);
//            result.put("success", true);
//            result.put("flowId", response.getData().getFlowId());
//            result.put("url", response.getData().getUrl());
//            result.put("shortLink", response.getData().getShortLink());
//            result.put("env",projectEnv);
//            result.put("msg", "人脸认证发起成功");
//        } catch (DefineException e) {
//            //logger.error("人脸认证发起失败", e);
//            result.put("success", false);
////            result.put("code", e.getCode());
//            result.put("msg", e.getMessage());
//        }
//        return result;
//    }

    /**
     * 获取状态描述
     *
     * @param status 状态码
     * @return 状态描述
     */
    private String getStatusDesc(String status) {
        switch (status) {
            case "0":
                return "未完成";
            case "1":
                return "认证通过";
            case "2":
                return "认证不通过";
            case "3":
                return "认证中断";
            default:
                return "未知状态";
        }
    }

    /**
     * 调用第三方短信接口发送验证码
     * @return 第三方接口响应
     */
    @GetMapping("/sendSms/{phoneNum}")
    public String sendSms(@PathVariable("phoneNum")String phoneNum) {
        String urlStr = smsIp +"/YX/DXFS";
        String charset = "UTF-8";
        StringBuilder response = new StringBuilder();
        if (!redisCache.exists("debt:sendSms:count:" + phoneNum)){
            redisCache.setCacheObject("debt:sendSms:count:" + phoneNum,1,28800L, TimeUnit.SECONDS);
        } else {
            int cacheObject = (int) redisCache.getCacheObject("debt:sendSms:count:" + phoneNum) ;
            if (cacheObject >= 3){
                throw new  RuntimeException ("发送次数过多.}");
            }
            redisCache.setCacheObject("debt:sendSms:count:" + phoneNum,cacheObject + 1,28800L, TimeUnit.SECONDS);
        }
        //(8小时合计不超过三千次)
        if (!redisCache.exists("debt:api:count")){
            redisCache.setCacheObject("debt:api:count",1,28800L, TimeUnit.SECONDS);
        } else {
            int cacheObject = (int) redisCache.getCacheObject("debt:api:count") ;
            if (cacheObject >= 3000){
                throw new  RuntimeException ("今日注册数量过多,请明日再来注册.");
            }
            redisCache.setCacheObject("debt:api:count:",cacheObject + 1,28800L, TimeUnit.SECONDS);
        }

        try {
            // 构建参数
            StringBuilder params = new StringBuilder();
            params.append("DesNo=").append(URLEncoder.encode(phoneNum, charset));
            Random random = new Random();
            int num = random.nextInt(1000000);
            String sixDigitCode = String.format("%06d", num);
//            sixDigitCode = "123456"; //fixme:短信
            redisCache.setCacheObject("debt:sendSms:" + phoneNum,sixDigitCode,3000, TimeUnit.SECONDS);
            String msg = "您注册验证码为" + sixDigitCode +"，请勿向他人泄露您的验证码，否则您将可能承担信息泄露的风险。如非本人操作请忽略本短信。" ;
            params.append("&Msg=").append(URLEncoder.encode(msg, charset));
            params.append("&source.system_id=").append("zhaizhuan");
            params.append("&autograph=").append("[北京聚汇融盛]");
            // 创建连接
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

            // 发送参数
            OutputStream os = conn.getOutputStream();
            os.write(params.toString().getBytes(charset));
            os.flush();
            os.close();

             //读取响应
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), charset));
            String line;
            while ((line = in.readLine()) != null) {
                response.append(line);
            }
            in.close();
            return response.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"success\":false,\"msg\":\"短信发送失败: " + e.getMessage() + "\"}";
        }
    }

    /**
     * 调用第三方短信接口发送验证码
     * @return 第三方接口响应
     */
//    @GetMapping("/login/sendSms/{phoneNum}")
//    public String loginSendSms(@PathVariable("phoneNum")String phoneNum) {
//        String urlStr = "http://47.104.69.109:8091/YX/DXFS";
//        String charset = "UTF-8";
//        StringBuilder response = new StringBuilder();
//        if (!redisCache.exists("debt:sendSms:login:count:" + phoneNum)){
//            redisCache.setCacheObject("debt:sendSms:login:count:" + phoneNum,1,28800, TimeUnit.SECONDS);
//        } else {
//            int cacheObject = (int) redisCache.getCacheObject("debt:sendSms:login:count:" + phoneNum) ;
//            if (cacheObject >= 3){
//                throw new ServiceException ("发送次数过多");
////                return "{\"code\":500,\"msg\":\"发送次数过多.\"}";
//            }
//            redisCache.setCacheObject("debt:sendSms:login:count:" + phoneNum,cacheObject + 1,28800, TimeUnit.SECONDS);
//        }
//        try {
//            // 构建参数
//            StringBuilder params = new StringBuilder();
//            params.append("DesNo=").append(URLEncoder.encode(phoneNum, charset));
//            Random random = new Random();
//            int num = random.nextInt(1000000);
//            String sixDigitCode = String.format("%06d", num);
//            redisCache.setCacheObject("debt:sendSms:login:" + phoneNum,sixDigitCode,3000, TimeUnit.SECONDS);
//            String msg = "【上海云信留客】尊敬的用户，您的验证码为" + sixDigitCode;
//            params.append("&Msg=").append(URLEncoder.encode(msg, charset));
//            params.append("&source.system_id=").append("zhaizhuan");
//
//            // 创建连接
//            URL url = new URL(urlStr);
//            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
//            conn.setRequestMethod("POST");
//            conn.setDoOutput(true);
//            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
//
//            // 发送参数
//            OutputStream os = conn.getOutputStream();
//            os.write(params.toString().getBytes(charset));
//            os.flush();
//            os.close();
//
//            // 读取响应
//            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), charset));
//            String line;
//            while ((line = in.readLine()) != null) {
//                response.append(line);
//            }
//            in.close();
//            return response.toString();
//            return "{\n" +
//                    "    \"code\": 200,\n" +
//                    "    \"msg\": \"ok\",\n" +
//                    "    \"data\": {\n" +
//                    "        \"code\": \"200\",\n" +
//                    "        \"data\": \"2505081482527995687\",\n" +
//                    "        \"message\": \"成功\"\n" +
//                    "    }\n" +
//                    "}";
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new ServiceException ("短信发送失败");
//            //return "{\"code\":500,\"msg\":\"短信发送失败: " + e.getMessage() + "\"}";
//        }
//
//    }

    @GetMapping("/sendSms/equals")
    public AjaxResult equalsSms(SmsEqual smsEqual) {
        String sms = redisCache.getCacheObject("debt:sendSms:" + smsEqual.getPhoneNum());
        boolean equals = smsEqual.getCode().equals(sms);
        if (equals){
            DebtUser debtUser = new DebtUser();
            debtUser.setIdCard(smsEqual.getIdNo());
            debtUser.setName(smsEqual.getName());
            debtUser.setPhoneNum(smsEqual.getPhoneNum());
            debtUser.setDataSource("1");
            debtUser.setOpenId(smsEqual.getOpenId());
            SysCompanyVo sysCompanyVo = new SysCompanyVo();
            sysCompanyVo.setCompanyShortName(smsEqual.getCustName());
            SysCompany sysCompany = companyService.selectSysCompanyByCompanyShortName(sysCompanyVo);
            if (sysCompany != null){
                debtUser.setCustId(sysCompany.getId());
            }
            debtUserService.insertDebtUser(debtUser);
            redisCache.deleteObject("debt:sendSms:" + smsEqual.getPhoneNum());
            AjaxResult ajax = AjaxResult.success();
            String token= tokenService.createToken(debtUser);
            ajax.put(Constants.TOKEN, token);
            return ajax;
        } else {
            throw new ServiceException("验证码错误");
        }
    }

    @GetMapping("/checkIdCard")
    public AjaxResult checkIdCard(DimBillNoInfo dimBillNoInfo)
    {
        if(dimBillNoInfoService.countByIdCardAndPhone(dimBillNoInfo) > 0){
            return AjaxResult.success("成功");
        } else {
            throw new ServiceException("尊敬的客户，请您提供与本人借款信息一致的手机号、身份证号进行注册，如所提供手机号、身份证号与借款信息不一致，将无法成功注册。");
        }
    }

//    @GetMapping("/checkAccount")
//    public AjaxResult checkAccount(DebtUser debtUser)
//    {
//        SysCompanyVo sysCompanyVo = new SysCompanyVo();
//        sysCompanyVo.setCompanyShortName(debtUser.getCustName());
//        SysCompany sysCompany = companyService.selectSysCompanyByCompanyShortName(sysCompanyVo);
//        if (sysCompany != null){
//            debtUser.setCustId(sysCompany.getId());
//        } else {
//            throw new ServiceException("请联系客服,确认担保公司名称是否正确");
//        }
//
//        if(!debtUserService.selectDebtUserList(debtUser).isEmpty()){
//            return AjaxResult.success("Y");
//        } else {
//            return AjaxResult.success("N");
//        }
//    }

    @GetMapping("/login")
    public AjaxResult login(DebtUser debtUser)
    {
        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyShortName(debtUser.getCustName());
        SysCompany sysCompany = companyService.selectSysCompanyByCompanyShortName(sysCompanyVo);
        if (sysCompany != null){
            debtUser.setCustId(sysCompany.getId());
        }
        String appid = env.getProperty("wechatApp."+ sysCompany.getCompanyCode() +".appId");
        String appSecret = env.getProperty("wechatApp."+ sysCompany.getCompanyCode() +".appSecret");

        String url = "https://api.weixin.qq.com/sns/jscode2session?" +
                "appid=" + appid +
                "&secret=" + appSecret +
                "&js_code=" + debtUser.getCode() +
                "&grant_type=authorization_code";

        // 调用微信接口
        String result = HttpUtils.sendGet(url);
        JSONObject json = JSON.parseObject(result);
//        if(json.getString("errcode") != null && !"".equals(json.getString("errcode"))){
//            throw new ServiceException(String.valueOf(json));
//        }

        String openid = json.getString("openid");
        AjaxResult ajax = AjaxResult.success();
//        ajax.put("weChatMsg", json);
        ajax.put("openId", openid);
        openid = "o6cvv61fvwqhMvbVXeWb0fx6IZ4Y";
        debtUser.setOpenId(openid);
        if(debtUserService.selectDebtUserListCount(debtUser) == 0){
            ajax.put("isReg", false);
        } else {
            String token= tokenService.createToken(debtUser);
            ajax.put(Constants.TOKEN, token);
            ajax.put("name", debtUser.getName());
            ajax.put("isReg", true);
        }
        return ajax;
    }

    @GetMapping("/register")
    public AjaxResult register(DebtUser debtUser) {
            debtUser.setDataSource("1");
            SysCompanyVo sysCompanyVo = new SysCompanyVo();
            sysCompanyVo.setCompanyShortName(debtUser.getCustName());
            SysCompany sysCompany = companyService.selectSysCompanyByCompanyShortName(sysCompanyVo);
            if (sysCompany != null){
                debtUser.setCustId(sysCompany.getId());
            }
            debtUserService.insertDebtUser(debtUser);
            AjaxResult ajax = AjaxResult.success();
            String token= tokenService.createToken(debtUser);
            ajax.put(Constants.TOKEN, token);
            return ajax;
    }

//    @GetMapping("/decodePhone")
//    public AjaxResult decodePhone(DecodePhone decodePhone) throws Exception {
//        SysCompanyVo sysCompanyVo = new SysCompanyVo();
//        sysCompanyVo.setCompanyShortName(decodePhone.getCustName());
//        SysCompany sysCompany = companyService.selectSysCompanyByCompanyShortName(sysCompanyVo);
//
//        String appId = env.getProperty("wechatApp."+ sysCompany.getCompanyCode() +".appId");
//        String appSecret = env.getProperty("wechatApp."+ sysCompany.getCompanyCode() +".appSecret");
//
//        // 1. 用code换取session_key
//        String sessionKey = getSessionKey(decodePhone.getCode(),appId, appSecret);
//
//        // 2. 解密手机号
//        String phoneNumber = decryptPhone(decodePhone.getEncryptedData(), decodePhone.getIv(), sessionKey);
//        AjaxResult ajaxResult = AjaxResult.success();
//        ajaxResult.put("phoneNumber", phoneNumber);
//        return ajaxResult;
//    }

    private String getSessionKey(String code,String appId ,String appSecret) {
        String url = "https://api.weixin.qq.com/sns/jscode2session?" +
                "appid=" + appId +
                "&secret=" + appSecret +
                "&js_code=" + code +
                "&grant_type=authorization_code";

        String response =  HttpUtils.sendGet(url); // 使用Hutool发送HTTP请求
        JSONObject json = JSON.parseObject(response);
        return json.getString("session_key");
    }

    private String decryptPhone(String encryptedData, String iv, String sessionKey)
            throws Exception {

        byte[] encryptedDataBytes = Base64.getDecoder().decode(encryptedData);
        byte[] ivBytes = Base64.getDecoder().decode(iv);
        byte[] sessionKeyBytes = Base64.getDecoder().decode(sessionKey);

        // 使用AES-CBC解密
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(sessionKeyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

        byte[] decrypted = cipher.doFinal(encryptedDataBytes);
        String result = new String(decrypted, StandardCharsets.UTF_8);

        // 解析JSON获取手机号
        JSONObject json = JSON.parseObject(result);
        return json.getString("purePhoneNumber"); // 示例: "13812341234"
    }

}

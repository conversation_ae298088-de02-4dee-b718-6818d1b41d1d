package com.ruoyi.code.debtConversion.controller;

import com.ruoyi.code.debtConversion.domain.DimBillNoInfo;
import com.ruoyi.code.debtConversion.domain.vo.DimBillNoInfoMini;
import com.ruoyi.code.debtConversion.service.IDimBillNoInfoService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 借据信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/dim/bill/no/info")
public class DimBillNoInfoController extends BaseController
{
    @Autowired
    private IDimBillNoInfoService dimBillNoInfoService;

    /**
     * 查询借据信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(DimBillNoInfo dimBillNoInfo)
    {
        startPage();
        List<DimBillNoInfo> list = dimBillNoInfoService.selectDimBillNoInfoList(dimBillNoInfo);
        return getDataTable(list);
    }

    /**
     * 导出借据信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "借据信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DimBillNoInfo dimBillNoInfo)
    {
        List<DimBillNoInfo> list = dimBillNoInfoService.selectDimBillNoInfoList(dimBillNoInfo);
        ExcelUtil<DimBillNoInfo> util = new ExcelUtil<DimBillNoInfo>(DimBillNoInfo.class);
        util.exportExcel(response, list, "借据信息数据");
    }

    /**
     * 获取借据信息详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{billAppNo}")
    public AjaxResult getInfo(@PathVariable("billAppNo") String billAppNo)
    {
        return AjaxResult.success(dimBillNoInfoService.selectDimBillNoInfoByBillAppNo(billAppNo));
    }

    /**
     * 新增借据信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "借据信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DimBillNoInfo dimBillNoInfo)
    {
        return toAjax(dimBillNoInfoService.insertDimBillNoInfo(dimBillNoInfo));
    }

    /**
     * 修改借据信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "借据信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DimBillNoInfo dimBillNoInfo)
    {
        return toAjax(dimBillNoInfoService.updateDimBillNoInfo(dimBillNoInfo));
    }

    /**
     * 删除借据信息
     */
    //@PreAuthorize("@ss.hasPermi('system:info:remove')")
    @Log(title = "借据信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{billAppNos}")
    public AjaxResult remove(@PathVariable String[] billAppNos)
    {
        return toAjax(dimBillNoInfoService.deleteDimBillNoInfoByBillAppNos(billAppNos));
    }

    @GetMapping("/miniList")
    public TableDataInfo miniList(DimBillNoInfo dimBillNoInfo)
    {
        startPage();
        List<DimBillNoInfoMini> list = dimBillNoInfoService.miniList(dimBillNoInfo);
        return getDataTable(list);
    }
}

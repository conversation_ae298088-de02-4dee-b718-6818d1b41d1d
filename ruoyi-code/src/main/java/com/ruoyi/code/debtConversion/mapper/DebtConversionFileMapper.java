package com.ruoyi.code.debtConversion.mapper;

import com.ruoyi.code.debtConversion.domain.DebtConversionFile;
import com.ruoyi.code.debtConversion.domain.vo.DebtConversionFileVo;

import java.util.List;

/**
 * 债转文件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface DebtConversionFileMapper
{
    /**
     * 查询债转文件
     *
     * @param id 债转文件主键
     * @return 债转文件
     */
    public DebtConversionFile selectDebtConversionFileById(Long id);

    /**
     * 查询债转文件列表
     *
     * @param debtConversionFile 债转文件
     * @return 债转文件集合
     */
    public List<DebtConversionFileVo> selectDebtConversionFileList(DebtConversionFileVo debtConversionFile);

    /**
     * 新增债转文件
     *
     * @param debtConversionFile 债转文件
     * @return 结果
     */
    public int insertDebtConversionFile(DebtConversionFile debtConversionFile);

    /**
     * 修改债转文件
     *
     * @param debtConversionFile 债转文件
     * @return 结果
     */
    public int updateDebtConversionFile(DebtConversionFile debtConversionFile);

    /**
     * 删除债转文件
     *
     * @param id 债转文件主键
     * @return 结果
     */
    public int deleteDebtConversionFileById(Long id);

    /**
     * 批量删除债转文件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDebtConversionFileByIds(Long[] ids);
}

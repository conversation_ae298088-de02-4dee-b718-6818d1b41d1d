package com.ruoyi.code.debtConversion.utils;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.Statement;
import java.util.Collection;
import java.util.List;
import java.util.Properties;

/**
 * AES加解密拦截器
 * 拦截MyBatis的SQL执行，自动处理加解密字段
 *
 * <AUTHOR>
 * @date 2024
 */
@Component
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
    @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class}),
    @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class AESInterceptor implements Interceptor {

    private static final Logger logger = LoggerFactory.getLogger(AESInterceptor.class);

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object target = invocation.getTarget();

        if (target instanceof Executor) {
            return handleExecutor(invocation);
        } else if (target instanceof ResultSetHandler) {
            return handleResultSet(invocation);
        } else if (target instanceof StatementHandler) {
            return handleStatement(invocation);
        }

        return invocation.proceed();
    }

    /**
     * 处理Executor拦截
     */
    private Object handleExecutor(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameter = args[1];

        // 对于INSERT和UPDATE操作，加密参数
        if (parameter != null && (mappedStatement.getSqlCommandType() == SqlCommandType.INSERT
                || mappedStatement.getSqlCommandType() == SqlCommandType.UPDATE)) {

            if (logger.isDebugEnabled()) {
                logger.debug("拦截到 {} 操作，准备加密字段，参数类型: {}",
                    mappedStatement.getSqlCommandType(), parameter.getClass().getSimpleName());
            }

            encryptParameter(parameter);
        }

        Object result = invocation.proceed();

        // 对于查询操作，解密结果
        if (result != null && mappedStatement.getSqlCommandType() == SqlCommandType.SELECT) {
            if (logger.isDebugEnabled()) {
                logger.debug("拦截到 SELECT 操作，准备解密字段");
            }

            decryptResult(result);
        }

        return result;
    }

    /**
     * 处理ResultSetHandler拦截
     */
    private Object handleResultSet(Invocation invocation) throws Throwable {
        Object result = invocation.proceed();

        if (result != null) {
            if (logger.isDebugEnabled()) {
                logger.debug("拦截到结果集处理，准备解密字段");
            }

            decryptResult(result);
        }

        return result;
    }

    /**
     * 处理StatementHandler拦截
     */
    private Object handleStatement(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();

        try {
            // 获取BoundSql和参数
            BoundSql boundSql = statementHandler.getBoundSql();
            Object parameterObject = boundSql.getParameterObject();

            if (parameterObject != null) {
                if (logger.isDebugEnabled()) {
                    logger.debug("拦截到SQL准备阶段，检查参数加密");
                }

                // 处理模糊搜索的SQL改写
                handleFuzzySearchSql(statementHandler, boundSql, parameterObject);
            }
        } catch (Exception e) {
            logger.warn("处理SQL语句时发生异常: {}", e.getMessage());
        }

        return invocation.proceed();
    }

    /**
     * 加密参数对象
     */
    private void encryptParameter(Object parameter) {
        if (parameter == null) {
            return;
        }

        try {
            if (logger.isDebugEnabled()) {
                logger.debug("开始加密参数，类型: {}", parameter.getClass().getSimpleName());
            }

            // 处理不同类型的参数
            if (parameter instanceof Collection) {
                // 直接是集合类型
                Collection<?> collection = (Collection<?>) parameter;
                if (logger.isDebugEnabled()) {
                    logger.debug("处理集合参数，大小: {}", collection.size());
                }
                for (Object item : collection) {
                    FieldEncryptionHelper.encryptFields(item);
                }
            } else if (parameter instanceof java.util.Map) {
                // MyBatis 可能将参数包装在 Map 中
                java.util.Map<?, ?> paramMap = (java.util.Map<?, ?>) parameter;
                if (logger.isDebugEnabled()) {
                    logger.debug("处理Map参数，键: {}", paramMap.keySet());
                }

                // 检查是否包含 "list" 键（批量操作的常见情况）
                if (paramMap.containsKey("list")) {
                    Object listParam = paramMap.get("list");
                    if (listParam instanceof Collection) {
                        Collection<?> collection = (Collection<?>) listParam;
                        if (logger.isDebugEnabled()) {
                            logger.debug("处理Map中的list参数，大小: {}", collection.size());
                        }
                        for (Object item : collection) {
                            FieldEncryptionHelper.encryptFields(item);
                        }
                    }
                } else {
                    // 处理Map中的其他值
                    for (Object value : paramMap.values()) {
                        if (value != null) {
                            FieldEncryptionHelper.encryptFields(value);
                        }
                    }
                }
            } else {
                // 单个对象
                FieldEncryptionHelper.encryptFields(parameter);
            }

            if (logger.isDebugEnabled()) {
                logger.debug("参数加密完成");
            }
        } catch (Exception e) {
            logger.error("加密参数失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 解密结果对象
     */
    private void decryptResult(Object result) {
        if (result == null) {
            return;
        }

        try {
            if (result instanceof List) {
                List<?> list = (List<?>) result;
                FieldEncryptionHelper.decryptFieldsList(list);
            } else if (result instanceof Collection) {
                Collection<?> collection = (Collection<?>) result;
                for (Object item : collection) {
                    FieldEncryptionHelper.decryptFields(item);
                }
            } else {
                FieldEncryptionHelper.decryptFields(result);
            }
        } catch (Exception e) {
            logger.error("解密结果失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理模糊搜索SQL改写
     */
    private void handleFuzzySearchSql(StatementHandler statementHandler, BoundSql boundSql, Object parameterObject) {
        try {
            String sql = boundSql.getSql();

            // 检查是否包含模糊搜索条件
            if (sql.toLowerCase().contains("like")) {
                if (logger.isDebugEnabled()) {
                    logger.debug("检测到LIKE查询，处理模糊搜索字段映射");
                }

                // 这里可以根据需要实现SQL改写逻辑
                // 将对加密字段的LIKE查询改写为对模糊搜索字段的查询
                handleLikeQueryRewrite(statementHandler, boundSql, parameterObject);
            }
        } catch (Exception e) {
            logger.warn("处理模糊搜索SQL改写时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 处理LIKE查询改写
     */
    private void handleLikeQueryRewrite(StatementHandler statementHandler, BoundSql boundSql, Object parameterObject) {
        // 这里可以实现具体的SQL改写逻辑
        // 例如：将 name LIKE '%value%' 改写为 name_search LIKE '%processedvalue%'
        // 具体实现可以根据业务需求进行定制

        if (logger.isDebugEnabled()) {
            logger.debug("LIKE查询改写处理完成");
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以从配置文件中读取相关配置
        if (logger.isDebugEnabled()) {
            logger.debug("AES拦截器配置加载完成");
        }
    }
}

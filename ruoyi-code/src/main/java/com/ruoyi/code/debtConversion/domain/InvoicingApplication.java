package com.ruoyi.code.debtConversion.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.code.debtConversion.utils.AESEncrypted;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 开票申请明细对象 dc_invoicing_application
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
public class InvoicingApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联上传文件 */
    @Excel(name = "关联上传文件")
    private Long fileId;

    /** 借款申请编号 */
    @Excel(name = "借款申请编号")
    private String loanCode;

    /** 借款人 */
    @Excel(name = "借款人")
    private String borrower;

    /** 手机号 */
    @Excel(name = "手机号")
    @AESEncrypted(key = "DEBT", enableFuzzySearch = false, format = AESEncrypted.EncryptionFormat.HEX)
    private String phoneNum;

    /** 身份证号 */
    @Excel(name = "身份证号")
    @AESEncrypted(key = "DEBT", enableFuzzySearch = false, format = AESEncrypted.EncryptionFormat.HEX)
    private String idCard;

    /** 申请开票金额(元) */
    @Excel(name = "申请开票金额(元)")
    private BigDecimal invoicingAmount;

    /** 资金方 */
    @Excel(name = "资金方")
    private Long fundId;

    /** 接收邮箱 */
    @Excel(name = "接收邮箱")
    private String receivingEmail;

    /** 资产方 */
    @Excel(name = "资产方")
    private Long partnerId;

    /** 借款金额(元) */
    @Excel(name = "借款金额(元)")
    private BigDecimal loanAmount;

    /** 借款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "借款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loanTime;

    /** 结清日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结清日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date settleTime;

    /**开票申请编号 */
    private String invoicingApplicationCode;

    /** 开票申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    //@Excel(name = "开票申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date InvoicingApplicationTime;
}

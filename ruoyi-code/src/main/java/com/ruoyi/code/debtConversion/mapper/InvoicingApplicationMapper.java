package com.ruoyi.code.debtConversion.mapper;

import com.ruoyi.code.debtConversion.domain.InvoicingApplication;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingApplicationImport;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingApplicationVo;

import java.util.List;

/**
 * 开票申请明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface InvoicingApplicationMapper
{
    /**
     * 查询开票申请明细
     *
     * @param id 开票申请明细主键
     * @return 开票申请明细
     */
    public InvoicingApplication selectInvoicingApplicationById(Long id);

    /**
     * 查询开票申请明细列表
     *
     * @param invoicingApplication 开票申请明细
     * @return 开票申请明细集合
     */
    public List<InvoicingApplicationVo> selectInvoicingApplicationList(InvoicingApplicationVo invoicingApplication);

    public List<InvoicingApplicationImport> selectInvoicingApplicationImport (InvoicingApplicationVo invoicingApplication);

    /**
     * 新增开票申请明细
     *
     * @param invoicingApplication 开票申请明细
     * @return 结果
     */
    public int insertInvoicingApplication(InvoicingApplication invoicingApplication);

    /**
     * 修改开票申请明细
     *
     * @param invoicingApplication 开票申请明细
     * @return 结果
     */
    public int updateInvoicingApplication(InvoicingApplication invoicingApplication);

    /**
     * 删除开票申请明细
     *
     * @param id 开票申请明细主键
     * @return 结果
     */
    public int deleteInvoicingApplicationById(Long id);

    /**
     * 批量删除开票申请明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInvoicingApplicationByIds(Long[] ids);

    /**
     * 批量新增开票申请明细
     *
     * @param invoicingApplication 开票申请明细
     * @return 结果
     */
    public int insertInvoicingApplicationBatch(List<InvoicingApplication> invoicingApplication);

    public int batchUpdateInvoicingApplicationCode(List<InvoicingApplicationVo> invoicingApplication);
}

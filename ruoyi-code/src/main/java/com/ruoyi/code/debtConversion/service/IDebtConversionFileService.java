package com.ruoyi.code.debtConversion.service;

import com.ruoyi.code.debtConversion.domain.DebtConversionFile;
import com.ruoyi.code.debtConversion.domain.vo.DebtConversionFileVo;
import com.ruoyi.code.debtConversion.domain.vo.DebtConversionImport;

import java.util.List;
import java.util.Map;

/**
 * 债转文件Service接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface IDebtConversionFileService
{
    /**
     * 查询债转文件
     *
     * @param id 债转文件主键
     * @return 债转文件
     */
    public DebtConversionFile selectDebtConversionFileById(Long id);

    /**
     * 查询债转文件列表
     *
     * @param debtConversionFile 债转文件
     * @return 债转文件集合
     */
    public List<DebtConversionFileVo> selectDebtConversionFileList(DebtConversionFileVo debtConversionFile);

    /**
     * 新增债转文件
     *
     * @param debtConversionFile 债转文件
     * @return 结果
     */
    public int insertDebtConversionFile(DebtConversionFile debtConversionFile);

    /**
     * 修改债转文件
     *
     * @param debtConversionFile 债转文件
     * @return 结果
     */
    public int updateDebtConversionFile(DebtConversionFile debtConversionFile);

    /**
     * 批量删除债转文件
     *
     * @param ids 需要删除的债转文件主键集合
     * @return 结果
     */
    public int deleteDebtConversionFileByIds(Long[] ids);

    /**
     * 删除债转文件信息
     *
     * @param id 债转文件主键
     * @return 结果
     */
    public int deleteDebtConversionFileById(Long id);

    public int importData(DebtConversionFileVo debtConversionFile);

    public Map<String,List<DebtConversionImport>> importDataCheck(List<DebtConversionImport> debtConversionList);

    public int pushDebtConversion(DebtConversionFile debtConversionFile);
}

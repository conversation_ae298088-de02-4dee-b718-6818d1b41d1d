package com.ruoyi.code.debtConversion.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 开票申请业务关联数据中间对象 dc_invoicing_middle
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
public class InvoicingMiddle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 开票业务 id */
    @Excel(name = "开票业务 id")
    private Long invoicingBusinessId;

    /** 关联数据 id */
    @Excel(name = "关联数据 id")
    private Long correlationId;

    /** 关联数据类型 1.对比数据 2.小程序数据*/
    @Excel(name = "关联数据类型")
    private String correlationType;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setInvoicingBusinessId(Long invoicingBusinessId)
    {
        this.invoicingBusinessId = invoicingBusinessId;
    }

    public Long getInvoicingBusinessId()
    {
        return invoicingBusinessId;
    }
    public void setCorrelationId(Long correlationId)
    {
        this.correlationId = correlationId;
    }

    public Long getCorrelationId()
    {
        return correlationId;
    }
    public void setCorrelationType(String correlationType)
    {
        this.correlationType = correlationType;
    }

    public String getCorrelationType()
    {
        return correlationType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("invoicingBusinessId", getInvoicingBusinessId())
            .append("correlationId", getCorrelationId())
            .append("correlationType", getCorrelationType())
            .toString();
    }
}

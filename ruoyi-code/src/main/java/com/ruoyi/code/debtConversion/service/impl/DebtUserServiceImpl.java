package com.ruoyi.code.debtConversion.service.impl;

import com.ruoyi.code.debtConversion.domain.DebtUser;
import com.ruoyi.code.debtConversion.domain.SysCompany;
import com.ruoyi.code.debtConversion.domain.vo.SysCompanyVo;
import com.ruoyi.code.debtConversion.mapper.DebtUserMapper;
import com.ruoyi.code.debtConversion.service.IDebtUserService;
import com.ruoyi.code.debtConversion.service.ISysCompanyService;
import com.ruoyi.code.debtConversion.utils.DesensitizationUtils;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 债转用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Service
public class DebtUserServiceImpl implements IDebtUserService
{
    @Autowired
    private DebtUserMapper debtUserMapper;
    @Autowired
    private ISysCompanyService companyService;
    /**
     * 查询债转用户
     *
     * @param id 债转用户主键
     * @return 债转用户
     */
    @Override
    public DebtUser selectDebtUserById(Long id)
    {
        return debtUserMapper.selectDebtUserById(id);
    }

    /**
     * 查询债转用户列表
     *
     * @param debtUser 债转用户
     * @return 债转用户
     */
    @Override
    public List<DebtUser> selectDebtUserList(DebtUser debtUser)
    {
        List<DebtUser> debtUsers = debtUserMapper.selectDebtUserList(debtUser);

        List<Long> conpanyIds = debtUsers.stream()
                .flatMap(dc -> Stream.of(
                        dc.getCustId()
                ))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(conpanyIds);
        List<SysCompanyVo> sysCompanyVos = companyService.selectSysCompanyList(sysCompanyVo);
        // 将 List<SysCompanyVo> 转换为 Map<Long, String>
        Map<Long, String> sysCompanyMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyShortName));
        debtUsers.forEach(vo-> {
            vo.setCustName(sysCompanyMap.get(vo.getCustId()));

            vo.setRealIdCard(vo.getIdCard());
            vo.setRealPhoneNum(vo.getPhoneNum());
            vo.setIdCard(DesensitizationUtils.desensitizeIdCard(vo.getIdCard()));
            vo.setPhoneNum(DesensitizationUtils.desensitizePhone(vo.getPhoneNum()));
        });
        return debtUsers;
    }

    @Override
    public Integer selectDebtUserListCount(DebtUser debtUser){
        return debtUserMapper.selectDebtUserListCount(debtUser);
    }

    /**
     * 新增债转用户
     *
     * @param debtUser 债转用户
     * @return 结果
     */
    @Override
    public int insertDebtUser(DebtUser debtUser)
    {
        debtUser.setCreateTime(DateUtils.getNowDate());
        return debtUserMapper.insertDebtUser(debtUser);
    }

    /**
     * 修改债转用户
     *
     * @param debtUser 债转用户
     * @return 结果
     */
    @Override
    public int updateDebtUser(DebtUser debtUser)
    {
        debtUser.setUpdateTime(DateUtils.getNowDate());
        return debtUserMapper.updateDebtUser(debtUser);
    }

    /**
     * 批量删除债转用户
     *
     * @param ids 需要删除的债转用户主键
     * @return 结果
     */
    @Override
    public int deleteDebtUserByIds(Long[] ids)
    {
        return debtUserMapper.deleteDebtUserByIds(ids);
    }

    /**
     * 删除债转用户信息
     *
     * @param id 债转用户主键
     * @return 结果
     */
    @Override
    public int deleteDebtUserById(Long id)
    {
        return debtUserMapper.deleteDebtUserById(id);
    }

    @Override
    public DebtUser getInfo(DebtUser debtUser)
    {
        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyShortName(debtUser.getCustName());
        SysCompany sysCompany = companyService.selectSysCompanyByCompanyShortName(sysCompanyVo);
        if (sysCompany != null){
            debtUser.setCustId(sysCompany.getId());
        }
        DebtUser user = debtUserMapper.selectDebtUserList(debtUser).stream().findFirst().orElse(null);
        if (user.getId() != null){
            user.setCustName(sysCompany.getCompanyShortName());
            user.setLastLoginTime(DateUtils.getNowDate());
            updateDebtUser(user);
        };
        return user;
    }
}

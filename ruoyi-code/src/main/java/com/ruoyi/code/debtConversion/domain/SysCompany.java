package com.ruoyi.code.debtConversion.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 全量公司信息对象 sys_company
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
@Data
public class SysCompany extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 公司简称 */
    @Excel(name = "公司简称")
    private String companyShortName;

    /** 是否为内部公司，1是 0否 */
    @Excel(name = "是否为内部公司，1是 0否")
    private String isInside;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1停用")
    private String status;

    /**审核状态 0新增审核状态，1修改项目审核中，2删除项目审核中，3（-） */
    private String checkStatus;

    /** 公司编码 */
    @Excel(name = "公司编码")
    private String companyCode;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    private String companyNo;

    /** 联系人 */
    @Excel(name = "联系人")
    private String linkman;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    private String email;

    /** 邮政编码 */
    @Excel(name = "邮政编码")
    private String postcode;

    /** 办公地址 */
    @Excel(name = "办公地址")
    private String businessAddress;

    /** 网站 */
    @Excel(name = "网站")
    private String website;

    /** 注册地址 */
    @Excel(name = "注册地址")
    private String registeredAddress;

    private String isDelete;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("companyName", getCompanyName())
            .append("companyShortName", getCompanyShortName())
            .append("isInside", getIsInside())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}

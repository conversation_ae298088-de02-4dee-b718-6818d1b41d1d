package com.ruoyi.code.debtConversion.service;

import com.ruoyi.code.debtConversion.domain.DebtConversion;
import com.ruoyi.code.debtConversion.domain.vo.DebtConversionImport;
import com.ruoyi.code.debtConversion.domain.vo.DebtConversionVo;

import java.util.List;

/**
 * 债转通知明细Service接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface IDebtConversionService
{
    /**
     * 查询债转通知明细
     *
     * @param id 债转通知明细主键
     * @return 债转通知明细
     */
    public DebtConversionVo selectDebtConversionById(Long id);

    public DebtConversionVo getAppUserInfo(Long id);
    /**
     * 查询债转通知明细列表
     *
     * @param debtConversion 债转通知明细
     * @return 债转通知明细集合
     */
    public List<DebtConversionVo> selectDebtConversionList(DebtConversionVo debtConversion);

    /**
     * 新增债转通知明细
     *
     * @param debtConversion 债转通知明细
     * @return 结果
     */
    public int insertDebtConversion(DebtConversion debtConversion);

    /**
     * 修改债转通知明细
     *
     * @param debtConversion 债转通知明细
     * @return 结果
     */
    public int updateDebtConversion(DebtConversion debtConversion);

    /**
     * 批量删除债转通知明细
     *
     * @param ids 需要删除的债转通知明细主键集合
     * @return 结果
     */
    public int deleteDebtConversionByIds(Long[] ids);

    /**
     * 删除债转通知明细信息
     *
     * @param id 债转通知明细主键
     * @return 结果
     */
    public int deleteDebtConversionById(Long id);

    /**
     * 删除债转通知明细信息
     *
     * @param id 债转通知明细主键
     * @return 结果
     */
    public int deleteDebtConversionByFildId(Long id);

    public List<DebtConversionImport> selectDebtConversionImportList(DebtConversionImport debtConversion);
    /**
     * 批量插入债权转换记录
     * @param list 债权转换记录列表
     * @return 插入的记录数
     */
    public int insertDebtConversionBatch(List<DebtConversion> list);

    public int batchUpdateDebtConversionCode(List<DebtConversionVo> debtConversion);

    /**
     * 修改开票申请编码
     *
     * @param debtConversion 债转通知明细
     * @return 结果
     */
    public int updateInvoicingApplicationCode(DebtConversionVo debtConversion);
}

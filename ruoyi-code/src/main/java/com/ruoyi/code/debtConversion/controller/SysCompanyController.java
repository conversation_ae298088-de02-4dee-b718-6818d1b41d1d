package com.ruoyi.code.debtConversion.controller;

import com.ruoyi.code.debtConversion.domain.vo.SysCompanyVo;
import com.ruoyi.code.debtConversion.service.ISysCompanyService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysOperLog;
import com.ruoyi.system.mapper.SysDictDataMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全量公司信息Controller
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
@RestController
@RequestMapping("/system/company")
public class SysCompanyController extends BaseController
{
    @Autowired
    private ISysCompanyService sysCompanyService;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    /**
     * 查询全量公司信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:company:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysCompanyVo sysCompany)
    {
        List<SysCompanyVo> list = sysCompanyService.selectSysCompanyList(sysCompany);
        return getDataTable(list);
    }

    @PostMapping("/listMore")
    public TableDataInfo listMore(@RequestBody SysCompanyVo sysCompany)
    {
        if (sysCompany.getMoreSearch() != null && !sysCompany.getMoreSearch().isEmpty()){
            Iterator<List<Long>> iterator = sysCompany.getMoreSearch().values().iterator();
            List<Long> intersection = new ArrayList<>(iterator.next());
            // 遍历map的所有List，并更新交集
            while (iterator.hasNext()) {
                List<Long> currentList = iterator.next();
                intersection.retainAll(currentList);  // 保留交集部分
            }
            sysCompany.setCompanyIdList(intersection);
        }
        List<SysCompanyVo> list = sysCompanyService.selectSysCompanyList(sysCompany);
        return getDataTable(list);
    }

    /**
     * 导出全量公司信息列表
     */
    //@PreAuthorize("@ss.hasPermi('system:company:export')")
    @Log(title = "全量公司信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysCompanyVo sysCompany)
    {
        List<SysCompanyVo> list = sysCompanyService.selectSysCompanyList(sysCompany);
        ExcelUtil<SysCompanyVo> util = new ExcelUtil<SysCompanyVo>(SysCompanyVo.class);
        util.exportExcel(response, list, "全量公司信息数据");
    }


}

package com.ruoyi.code.debtConversion.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DimBillNoInfoMini {
    /**
     * 借据金额
     */
    private BigDecimal loanAmt;
    /**
     * 借据起止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loanDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dueDate;
    /**
     * 平台方
     */
    private String platformName;
    /**
     * 资金方
     */
    private String financerName;
}

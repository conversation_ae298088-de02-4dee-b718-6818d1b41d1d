package com.ruoyi.code.debtConversion.domain.vo;

import com.ruoyi.code.debtConversion.domain.SysCompany;
import com.ruoyi.system.domain.SysPost;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SysCompanyVo extends SysCompany {

    /**
     * 公司类型
     */
    private Long companyTypeCode;

    /**
     * 公司支持业务类型
     */
    private Long companyBusinessTypeCode;
    /**
     * 类型 (键值)
     */
    private List<Long> companyTypeCodes;

    /**
     * 支持业务类型
     */
    private List<Long> companyBusinessTypeCodes;
    /**
     * 合作项目数量
     */
    private int projectCount ;

    /**
     * 公司id集合，查询前置条件 - 接参数用
     */
    private List<Long> companyIdList;

    /**
     * 岗位集合
     */
    private SysPost sysPost;

    /**
     * 来源(此参数作为立项项目管理模块新增公司类型字典的来源 xmgl:项目管理模块)
     * */
    private String addSource;

    /**
     * 返回的临时表uuid
     * */
    private String addUuid;

    /** 数据来源(1公司信息 2立项项目) */
    private String source;

    /** 岗位编码 */
    private String companyCode;

    /**
     * 权限 code
     */
    private String AuthModuleEnumCode;

    /**
     *  内外部
     */
    private String auxiliaryField;

    //更多查询条件
    private Map<String, List<Long>> moreSearch;

    private Long pageNum;

    private Long pageSize;

    private List<String> companyTypeCodeNameList;

    private String SelectCode;
}

package com.ruoyi.code.debtConversion.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;


import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 债转通知明细对象 dc_debt_conversion
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
public class DebtConversionImport extends BaseEntity
{


    /** 借款申请编号 */
    @Excel(name = "借款申请编号")
    private String loanCode;

    /** 借款人 */
    @Excel(name = "借款人")
    private String borrower;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phoneNum;

    /** 借款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "借款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loanTime;

    /** 担保时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "担保时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date guaranteeTime;

    /** 担保公司 */
    @Excel(name = "担保公司")
    private String custName;

    /** 资产方 */
    @Excel(name = "资产方")
    private String partnerName;

    /** 资金方 */
    @Excel(name = "资金方")
    private String fundName;

    /** 借款金额 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "借款金额", width = 30, dateFormat = "yyyy-MM-dd")
    private BigDecimal loanAmount;

    /** 债权接收方 */
    @Excel(name = "债权接收方")
    private String debtRecipientName;

    /**
     * 资产方
     */
    private Long custId;

    /**
     * 资产方
     */
    private Long partnerId;

    /**
     * 资金方
     */
    private Long fundId;

    /**
     * 债权接收方
     */
    private Long debtRecipientId;

    /**
    * 关联主表文件 id
     */
    private Long fileId;

    /**
     * 借款申请编号list
     */
    private List<String> loanCodeList;
}

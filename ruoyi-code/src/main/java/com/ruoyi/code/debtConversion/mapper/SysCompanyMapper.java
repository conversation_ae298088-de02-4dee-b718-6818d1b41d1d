package com.ruoyi.code.debtConversion.mapper;

import com.ruoyi.code.debtConversion.domain.SysCompany;
import com.ruoyi.code.debtConversion.domain.vo.SysCompanyVo;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.system.domain.SysOperLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 全量公司信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
public interface SysCompanyMapper
{

    /**
     * 查询全量公司信息列表
     *
     * @param sysCompany 全量公司信息
     * @return 全量公司信息集合
     */
    public List<SysCompanyVo> selectSysCompanyList(SysCompanyVo sysCompany);


    List<SysCompanyVo> selectCompanyListByCompanyShortNames(Set<String> companyNames);

    public SysCompanyVo selectSysCompanyById(Long id);

    public SysCompany selectSysCompanyByCompanyShortName(SysCompany sysCompany);
}

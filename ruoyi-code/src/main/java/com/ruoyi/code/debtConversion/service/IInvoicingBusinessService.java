package com.ruoyi.code.debtConversion.service;

import com.ruoyi.code.debtConversion.domain.InvoicingBusiness;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingBusinessVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 开票申请业务Service接口
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface IInvoicingBusinessService
{
    /**
     * 查询开票申请业务
     *
     * @param id 开票申请业务主键
     * @return 开票申请业务
     */
    public InvoicingBusinessVo selectInvoicingBusinessById(Long id);

    /**
     * 查询开票申请业务列表
     *
     * @param invoicingBusiness 开票申请业务
     * @return 开票申请业务集合
     */
    public List<InvoicingBusinessVo> selectInvoicingBusinessList(InvoicingBusinessVo invoicingBusiness);

    /**
     * 新增开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    public int insertInvoicingBusiness(InvoicingBusiness invoicingBusiness);

    /**
     * 新增开票申请业务
     *
     * @param invoicingBusinessList 开票申请业务
     * @return 结果
     */
    public int insertInvoicingBusinessBatch(List<InvoicingBusiness> invoicingBusinessList);

    /**
     * 修改开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    public int updateInvoicingBusiness(InvoicingBusiness invoicingBusiness);

//    public int singleinvoice(InvoicingBusiness invoicingBusiness);

    /**
     * 修改开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
//    public int uploadInvoice(MultipartFile file, InvoicingBusinessVo invoicingBusiness);

    /**
     * 批量删除开票申请业务
     *
     * @param ids 需要删除的开票申请业务主键集合
     * @return 结果
     */
    public int deleteInvoicingBusinessByIds(Long[] ids);

    /**
     * 删除开票申请业务信息
     *
     * @param id 开票申请业务主键
     * @return 结果
     */
    public int deleteInvoicingBusinessById(Long id);

    /**
     * 提交开票申请
     * @param invoicingBusinessVo
     * @return
     */
    public int invoicingBusinessBatch(InvoicingBusinessVo invoicingBusinessVo);

    public Map<String, Object> invoiceStatistics(InvoicingBusinessVo invoicingBusinessVo);

    /**
     * 校验开票申请文件信息
     *
     * @param invoicingBusinessVoList
     * @return 结果
     */
    public Map<String,List<InvoicingBusinessVo>> importDataCheck(List<InvoicingBusinessVo> invoicingBusinessVoList);

    /**
     * 校验开票申请文件信息
     *
     * @param invoicingBusinessVo
     * @return 结果
     */
    public int importData(InvoicingBusinessVo invoicingBusinessVo);

    public int miniProgramInvoicing(InvoicingBusinessVo invoicingBusiness);
}

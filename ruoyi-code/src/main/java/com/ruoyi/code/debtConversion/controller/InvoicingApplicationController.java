package com.ruoyi.code.debtConversion.controller;

import com.ruoyi.code.debtConversion.domain.InvoicingApplication;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingApplicationVo;
import com.ruoyi.code.debtConversion.service.IInvoicingApplicationService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * 开票申请明细Controller
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@RestController
@RequestMapping("/invoicing/application")
public class InvoicingApplicationController extends BaseController
{
    @Autowired
    private IInvoicingApplicationService invoicingApplicationService;

    /**
     * 查询开票申请明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:application:list')")
    @GetMapping("/list")
    public TableDataInfo list(InvoicingApplicationVo invoicingApplication)
    {
        startPage();
        List<InvoicingApplicationVo> list = invoicingApplicationService.selectInvoicingApplicationList(invoicingApplication);
        TableDataInfo dataTable = getDataTable(list);
        HashMap<Object, Object> countMap = new HashMap<>();

        long sameCount = 0;
        if (list != null) {
            for (InvoicingApplicationVo vo : list) {
                if (vo != null && Boolean.TRUE.equals(vo.getInvoicingAmountDiff())) {
                    sameCount++;
                }
            }
        }

        long diffCount = (list != null ? list.size() : 0) - sameCount;

        countMap.put("same", sameCount);
        countMap.put("diff", diffCount);
        dataTable.setMap(countMap);
        return dataTable;
    }

    /**
     * 导出开票申请明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:application:export')")
    @Log(title = "开票申请明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InvoicingApplicationVo invoicingApplication)
    {
        //FIXME :导出功能是明细二期中处理
        throw new RuntimeException("导出明细功能暂未开放");
//        List<InvoicingApplicationVo> list = invoicingApplicationService.selectInvoicingApplicationList(invoicingApplication);
//        ExcelUtil<InvoicingApplicationVo> util = new ExcelUtil<InvoicingApplicationVo>(InvoicingApplicationVo.class);
//        util.exportExcel(response, list, "开票申请明细数据");
    }

    /**
     * 获取开票申请明细详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:application:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(invoicingApplicationService.selectInvoicingApplicationById(id));
    }

    /**
     * 新增开票申请明细
     */
    //@PreAuthorize("@ss.hasPermi('system:application:add')")
    @Log(title = "开票申请明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InvoicingApplication invoicingApplication)
    {
        return toAjax(invoicingApplicationService.insertInvoicingApplication(invoicingApplication));
    }

    /**
     * 修改开票申请明细
     */
    //@PreAuthorize("@ss.hasPermi('system:application:edit')")
    @Log(title = "开票申请明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InvoicingApplication invoicingApplication)
    {
        return toAjax(invoicingApplicationService.updateInvoicingApplication(invoicingApplication));
    }

    /**
     * 删除开票申请明细
     */
    //@PreAuthorize("@ss.hasPermi('system:application:remove')")
    @Log(title = "开票申请明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(invoicingApplicationService.deleteInvoicingApplicationByIds(ids));
    }

    /**
     * 合并开票
     */
    //@PreAuthorize("@ss.hasPermi('system:file:query')")
    @GetMapping(value = "/implement")
    public AjaxResult implementInvoicingApplication(InvoicingApplicationVo invoicingApplication)
    {
        return AjaxResult.success(invoicingApplicationService.implementInvoicingApplication(invoicingApplication));
    }
}

package com.ruoyi.code.debtConversion.controller;

import com.ruoyi.code.debtConversion.domain.DebtConversion;
import com.ruoyi.code.debtConversion.domain.vo.DebtConversionVo;
import com.ruoyi.code.debtConversion.service.IDebtConversionService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 债转通知明细Controller
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@RestController
@RequestMapping("/debt/conversion")
public class DebtConversionController extends BaseController
{
    @Autowired
    private IDebtConversionService debtConversionService;

    /**
     * 查询债转通知明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:conversion:list')")
    @GetMapping("/list")
    public TableDataInfo list(DebtConversionVo debtConversion)
    {
        startPage();
        List<DebtConversionVo> list = debtConversionService.selectDebtConversionList(debtConversion);
        return getDataTable(list);
    }

    /**
     * 导出债转通知明细列表
     */
    //@PreAuthorize("@ss.hasPermi('system:conversion:export')")
    @Log(title = "债转通知明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DebtConversionVo debtConversion)
    {
        List<DebtConversionVo> list = debtConversionService.selectDebtConversionList(debtConversion);
        ExcelUtil<DebtConversionVo> util = new ExcelUtil<DebtConversionVo>(DebtConversionVo.class);
        util.exportExcel(response, list, "债转通知明细数据");
    }

    /**
     * 获取债转通知明细详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:conversion:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(debtConversionService.selectDebtConversionById(id));
    }

    @GetMapping(value = "/appUser/{id}")
    public AjaxResult getAppUserInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(debtConversionService.getAppUserInfo(id));
    }


    /**
     * 新增债转通知明细
     */
    //@PreAuthorize("@ss.hasPermi('system:conversion:add')")
    @Log(title = "债转通知明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DebtConversion debtConversion)
    {
        return toAjax(debtConversionService.insertDebtConversion(debtConversion));
    }

    /**
     * 修改债转通知明细
     */
    //@PreAuthorize("@ss.hasPermi('system:conversion:edit')")
    @Log(title = "债转通知明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DebtConversion debtConversion)
    {
        return toAjax(debtConversionService.updateDebtConversion(debtConversion));
    }

    /**
     * 删除债转通知明细
     */
    //@PreAuthorize("@ss.hasPermi('system:conversion:remove')")
    @Log(title = "债转通知明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(debtConversionService.deleteDebtConversionByIds(ids));
    }
}

package com.ruoyi.code.debtConversion.utils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * AES加解密注解
 * 用于标记需要进行AES加解密的实体类字段
 *
 * <AUTHOR>
 * @date 2024
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AESEncrypted {

    /**
     * 加密密钥，如果不指定则使用默认密钥
     * @return 加密密钥
     */
    String key() default "";

    /**
     * 是否启用模糊搜索支持
     * 启用后会在数据库中额外存储用于模糊搜索的处理字段
     * @return 是否启用模糊搜索
     */
    boolean enableFuzzySearch() default true;

    /**
     * 模糊搜索字段后缀
     * 当启用模糊搜索时，会自动创建一个以此后缀结尾的字段用于模糊搜索
     * @return 模糊搜索字段后缀
     */
    String fuzzySearchSuffix() default "_search";

    /**
     * 加密存储格式
     * @return 存储格式
     */
    EncryptionFormat format() default EncryptionFormat.BASE64;

    /**
     * 加密存储格式枚举
     */
    enum EncryptionFormat {
        /**
         * Base64编码格式（默认）
         */
        BASE64,

        /**
         * MySQL HEX格式
         */
        HEX,

        /**
         * 原始二进制格式
         */
        BINARY
    }
}

package com.ruoyi.code.debtConversion.controller;

import com.ruoyi.code.debtConversion.domain.DebtConversionFile;
import com.ruoyi.code.debtConversion.domain.until.CheckExcelTemplate;
import com.ruoyi.code.debtConversion.domain.vo.DebtConversionFileVo;
import com.ruoyi.code.debtConversion.domain.vo.DebtConversionImport;
import com.ruoyi.code.debtConversion.service.IDebtConversionFileService;
import com.ruoyi.code.debtConversion.service.IDebtConversionService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 债转文件Controller
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@RestController
@RequestMapping("/debt/conversion/file")
public class DebtConversionFileController extends BaseController
{
    @Autowired
    private IDebtConversionFileService debtConversionFileService;
    @Autowired
    private IDebtConversionService debtConversionService;
    /**
     * 查询债转文件列表
     */
    //@PreAuthorize("@ss.hasPermi('system:file:list')")
    @GetMapping("/list")
    public TableDataInfo list(DebtConversionFileVo debtConversionFile)
    {
//        startPage();
        List<DebtConversionFileVo> list = debtConversionFileService.selectDebtConversionFileList(debtConversionFile);
        return getDataTable(list);
    }

    /**
     * 导出债转文件列表
     */
    //@PreAuthorize("@ss.hasPermi('system:file:export')")
    @Log(title = "债转文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DebtConversionImport debtConversionImport)
    {
        List<DebtConversionImport> list = debtConversionService.selectDebtConversionImportList(debtConversionImport);
        ExcelUtil<DebtConversionImport> util = new ExcelUtil<DebtConversionImport>(DebtConversionImport.class);
        util.exportExcel(response, list, "债转文件数据");
    }

    /**
     * 获取债转文件详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:file:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(debtConversionFileService.selectDebtConversionFileById(id));
    }

    /**
     * 新增债转文件
     */
    //@PreAuthorize("@ss.hasPermi('system:file:add')")
    @Log(title = "债转文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DebtConversionFile debtConversionFile)
    {
        return toAjax(debtConversionFileService.insertDebtConversionFile(debtConversionFile));
    }

    /**
     * 新增债转文件
     */
    //@PreAuthorize("@ss.hasPermi('system:file:add')")
    //@Log(title = "债转文件", businessType = BusinessType.INSERT)
    @PutMapping("/pushDebtConversion")
    public AjaxResult pushDebtConversion(@RequestBody DebtConversionFile debtConversionFile)
    {
        return toAjax(debtConversionFileService.pushDebtConversion(debtConversionFile));
    }

    /**
     * 修改债转文件
     */
    //@PreAuthorize("@ss.hasPermi('system:file:edit')")
    @Log(title = "债转文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DebtConversionFile debtConversionFile)
    {
        return toAjax(debtConversionFileService.updateDebtConversionFile(debtConversionFile));
    }

    /**
     * 删除债转文件
     */
    //@PreAuthorize("@ss.hasPermi('system:file:remove')")
    @Log(title = "债转文件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(debtConversionFileService.deleteDebtConversionFileById(id));
    }

    /**
     * 导入数据
     * @param debtConversionFileVo
     * @return
     * @throws Exception
     */
    @Log(title = "债转信息导入数据", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importData")
    public AjaxResult importData(@RequestBody DebtConversionFileVo debtConversionFileVo) throws Exception {
        return AjaxResult.success(debtConversionFileService.importData(debtConversionFileVo));
    }

    @Log(title = "债转信息导入校验", businessType = BusinessType.IMPORT)
    @PostMapping("/importDataCheck")
    public AjaxResult importDataCheck(MultipartFile file) throws Exception {
        CheckExcelTemplate.validateExcelTemplate(file, DebtConversionImport.class);
        ExcelUtil<DebtConversionImport> util = new ExcelUtil<DebtConversionImport>(DebtConversionImport.class);
        List<DebtConversionImport> debtConversionList = util.importExcel(file.getInputStream());
        if(debtConversionList.isEmpty()){
            throw new RuntimeException("检查导入模版是否正确或至少导入一条数据");
        }
        Map<String, List<DebtConversionImport>> map = debtConversionFileService.importDataCheck(debtConversionList);
        return AjaxResult.success(map);
    }

    /**
     * 债转信息导入模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DebtConversionImport> util = new ExcelUtil<DebtConversionImport>(DebtConversionImport.class);
        util.importTemplateExcel(response, "债转信息导入模板");
    }

}

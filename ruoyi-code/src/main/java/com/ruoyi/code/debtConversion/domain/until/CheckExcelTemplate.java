package com.ruoyi.code.debtConversion.domain.until;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CheckExcelTemplate {

    // ... 其他方法 ...

    /**
     * 校验导入的Excel模板表头是否与VO类定义的表头一致
     * @param file 用户上传的Excel文件
     * @param voClass 对应的VO类，包含@Excel注解
     * @throws ServiceException 如果校验失败
     */
    public static void validateExcelTemplate(MultipartFile file, Class<?> voClass) throws ServiceException {
        List<String> expectedHeaders = getExpectedHeadersFromVo(voClass);
        List<String> actualHeaders = readHeadersFromUploadedFile(file);

        if (expectedHeaders.isEmpty()) {
            throw new ServiceException("模板定义错误：请检查导入模版是否有误。");
        }
        if (actualHeaders.isEmpty()) {
            throw new ServiceException("上传的文件没有表头行，或者表头行为空。");
        }

        // 1. 校验列的数量 (可以根据需求调整，例如允许上传文件列数更多，但至少包含模板定义的列)
        if (actualHeaders.size() < expectedHeaders.size()) {
            throw new ServiceException(String.format("模板列数不匹配。期望至少 %d 列，实际文件有 %d 列。请使用正确的模板。",
                    expectedHeaders.size(), actualHeaders.size()));
        }

        // 2. 逐个校验表头名称和顺序 (只比较模板定义的列)
        for (int i = 0; i < expectedHeaders.size(); i++) {
            String expectedHeader = expectedHeaders.get(i).trim();
            String actualHeader = actualHeaders.get(i).trim();
            if (!expectedHeader.equalsIgnoreCase(actualHeader)) { // 忽略大小写比较
                throw new ServiceException(String.format("模板表头不匹配。第 %d 列期望为 '%s'，实际为 '%s'。请使用正确的模板。",
                        i + 1, expectedHeader, actualHeader));
            }
        }
        // 如果校验通过，则不执行任何操作
    }

    /**
     * 从VO类中提取由@ExcelCheck或@Excel注解定义的期望表头列表（按sort排序）
     * @ExcelCheck 优先，并排除 type = EXPORT 的字段
     */
    private static List<String> getExpectedHeadersFromVo(Class<?> clazz) {
        // 使用 getAllFieldsList 来获取类及其父类的所有字段
        List<Field> fields = FieldUtils.getAllFieldsList(clazz);
        List<ExpectedHeaderInfo> headerInfos = new ArrayList<>();

        for (Field field : fields) {
            String headerName = null;
            int headerSort = 0;
            boolean addHeader = false; // Flag to determine if header should be added

            // 优先检查 @ExcelCheck 注解
            ExcelCheck excelCheckAnnotation = field.getAnnotation(ExcelCheck.class);
            if (excelCheckAnnotation != null && StringUtils.isNotEmpty(excelCheckAnnotation.name())) {
                // 检查 type 属性，如果不是 EXPORT，则可以加入
                if (excelCheckAnnotation.type() != ExcelCheck.Type.EXPORT) {
                    headerName = excelCheckAnnotation.name();
                    headerSort = excelCheckAnnotation.sort();
                    addHeader = true;
                }
                // 如果 type 是 EXPORT，则不处理此注解，addHeader 保持 false，跳过后续的@Excel检查
                else {
                    // Explicitly skip to next field if @ExcelCheck is EXPORT, to avoid @Excel fallback
                    continue;
                }
            }

            // 如果没有通过 @ExcelCheck 添加 (addHeader is false)，则检查 @Excel 注解
            if (!addHeader) {
                Excel excelAnnotation = field.getAnnotation(Excel.class);
                if (excelAnnotation != null && excelAnnotation.type() != Excel.Type.EXPORT && StringUtils.isNotEmpty(excelAnnotation.name())) {
                    headerName = excelAnnotation.name();
                    headerSort = excelAnnotation.sort();
                    addHeader = true;
                }
            }

            if (addHeader) {
                headerInfos.add(new ExpectedHeaderInfo(headerName, headerSort));
            }
        }

        // 根据注解的 sort 属性排序
        headerInfos.sort(Comparator.comparingInt(ExpectedHeaderInfo::getSort));

        return headerInfos.stream().map(ExpectedHeaderInfo::getName).collect(Collectors.toList());
    }

    // 辅助内部类用于排序
    private static class ExpectedHeaderInfo {
        private String name;
        private int sort;
        public ExpectedHeaderInfo(String name, int sort) {
            this.name = name;
            this.sort = sort;
        }
        public String getName() { return name; }
        public int getSort() { return sort; }
    }


    /**
     * 从上传的Excel文件中读取第一行作为实际表头列表
     */
    private static List<String> readHeadersFromUploadedFile(MultipartFile file) throws ServiceException {
        List<String> actualHeaders = new ArrayList<>();
        Workbook workbook = null;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            // WorkbookFactory.create 会自动识别 .xls 和 .xlsx格式
            workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 假设数据在第一个sheet

            if (sheet == null) {
                throw new ServiceException("Excel文件中没有工作表。");
            }

            Row headerRow = sheet.getRow(0); // 假设表头在第一行 (0-indexed)
            if (headerRow == null) {
                throw new ServiceException("Excel文件的工作表中没有表头行。");
            }

            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
                if (cell != null && cell.getCellType() == CellType.STRING) {
                    actualHeaders.add(cell.getStringCellValue().trim());
                } else if (cell != null && cell.getCellType() == CellType.NUMERIC) {
                    // 如果表头可能是数字，需要转换为字符串
                    DataFormatter dataFormatter = new DataFormatter();
                    actualHeaders.add(String.valueOf(dataFormatter.formatCellValue(cell)).trim());
                }
                else {
                    actualHeaders.add(""); // 对于空单元格或非字符串/数字单元格，添加空字符串
                }
            }
        } catch (IOException e) {
            // Log.error("读取上传的Excel表头失败", e);
            throw new ServiceException("读取Excel表头失败：" + e.getMessage());
        } catch (org.apache.poi.EncryptedDocumentException e) {
            // Log.error("Excel文件已加密", e);
            throw new ServiceException("Excel文件已加密，无法读取。");
        }
        finally {
            try {
                if (workbook != null) workbook.close();
                if (inputStream != null) inputStream.close();
            } catch (IOException e) {
                // Log.error("关闭Excel资源失败", e);
            }
        }
        return actualHeaders;
    }
    // ... importUser 方法等 ...
}

package com.ruoyi.code.debtConversion.service;

import com.ruoyi.code.debtConversion.domain.DimBaseProdInfo;

import java.util.List;

/**
 * 产品信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
public interface IDimBaseProdInfoService
{
    /**
     * 查询产品信息
     *
     * @param id 产品信息主键
     * @return 产品信息
     */
    public DimBaseProdInfo selectDimBaseProdInfoById(Long id);

    /**
     * 查询产品信息列表
     *
     * @param dimBaseProdInfo 产品信息
     * @return 产品信息集合
     */
    public List<DimBaseProdInfo> selectDimBaseProdInfoList(DimBaseProdInfo dimBaseProdInfo);

    /**
     * 新增产品信息
     *
     * @param dimBaseProdInfo 产品信息
     * @return 结果
     */
    public int insertDimBaseProdInfo(DimBaseProdInfo dimBaseProdInfo);

    /**
     * 修改产品信息
     *
     * @param dimBaseProdInfo 产品信息
     * @return 结果
     */
    public int updateDimBaseProdInfo(DimBaseProdInfo dimBaseProdInfo);

    /**
     * 批量删除产品信息
     *
     * @param ids 需要删除的产品信息主键集合
     * @return 结果
     */
    public int deleteDimBaseProdInfoByIds(Long[] ids);

    /**
     * 删除产品信息信息
     *
     * @param id 产品信息主键
     * @return 结果
     */
    public int deleteDimBaseProdInfoById(Long id);
}

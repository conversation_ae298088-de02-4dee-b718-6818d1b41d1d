package com.ruoyi.code.debtConversion.controller;

import com.ruoyi.code.debtConversion.domain.InvoicingBusiness;
import com.ruoyi.code.debtConversion.domain.until.CheckExcelTemplate;
import com.ruoyi.code.debtConversion.domain.until.ExcelUtilCheck;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingBusinessVo;
import com.ruoyi.code.debtConversion.service.IInvoicingBusinessService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.file.FileUploadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

/**
 * 开票申请业务Controller
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@RestController
@RequestMapping("/invoicing/business")
public class InvoicingBusinessController extends BaseController
{
    @Autowired
    private IInvoicingBusinessService invoicingBusinessService;

    /**
     * 查询开票申请业务列表
     */
    //@PreAuthorize("@ss.hasPermi('system:business:list')")
    @GetMapping("/list")
    public TableDataInfo list(InvoicingBusinessVo invoicingBusiness)
    {
        startPage();
        List<InvoicingBusinessVo> list = invoicingBusinessService.selectInvoicingBusinessList(invoicingBusiness);
        return getDataTable(list);
    }

    /**
     * 导出开票申请业务列表
     */
    //@PreAuthorize("@ss.hasPermi('system:business:export')")
    @Log(title = "开票申请业务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InvoicingBusinessVo invoicingBusiness)
    {
        List<InvoicingBusinessVo> list = invoicingBusinessService.selectInvoicingBusinessList(invoicingBusiness);
        IntStream.range(0, list.size()).forEach(i -> list.get(i).setSerialNumber(i + 1));
        ExcelUtilCheck<InvoicingBusinessVo> util = new ExcelUtilCheck<InvoicingBusinessVo>(InvoicingBusinessVo.class);
        if ("2".equals(invoicingBusiness.getExportTpye())){
            util.hideColumn("invoicingApplicationTime","receivingEmail");
        }
        util.exportExcel(response, list, "开票申请数据");
    }

    /**
     * 获取开票申请业务详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:business:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(invoicingBusinessService.selectInvoicingBusinessById(id));
    }

    /**
     * 新增开票申请业务
     */
    //@PreAuthorize("@ss.hasPermi('system:business:add')")
    @Log(title = "开票申请业务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InvoicingBusinessVo invoicingBusiness)
    {
        return toAjax(invoicingBusinessService.insertInvoicingBusiness(invoicingBusiness));
    }

    /**
     * 修改开票申请业务
     */
    //@PreAuthorize("@ss.hasPermi('system:business:edit')")
    @Log(title = "开票申请业务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InvoicingBusiness invoicingBusiness)
    {
        return toAjax(invoicingBusinessService.updateInvoicingBusiness(invoicingBusiness));
    }

//    @Log(title = "单个上传(修改)发票", businessType = BusinessType.UPDATE)
//    @PostMapping("/singleinvoice")
//    public AjaxResult singleinvoice(@RequestBody InvoicingBusiness invoicingBusiness)
//    {
//        return toAjax(invoicingBusinessService.singleinvoice(invoicingBusiness));
//    }

//    /**
//     * 保存确定上传发票(批量上传匹配发票)
//     */
//    @Log(title = "保存确定上传发票", businessType = BusinessType.OTHER)
//    @PostMapping("/uploadInvoice")
//    public AjaxResult uploadInvoice(MultipartFile file,InvoicingBusinessVo invoicingBusiness)
//    {
//        return toAjax(invoicingBusinessService.uploadInvoice(file,invoicingBusiness));
//    }

    /**
     * 删除开票申请业务
     */
    //@PreAuthorize("@ss.hasPermi('system:business:remove')")
    @Log(title = "开票申请业务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(invoicingBusinessService.deleteInvoicingBusinessByIds(ids));
    }

//    @Anonymous
//    @PostMapping("/uploadFile")
//    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) {
//        try {
//            String name = file.getOriginalFilename();
//            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.INVOICING_BUSINESS, file);
//            AjaxResult ajax = AjaxResult.success();
//            ajax.put("url", url);
//            ajax.put("name", name);
//            return ajax;
//        } catch (Exception e) {
//            return AjaxResult.error(e.getMessage());
//        }
//    }

    /**
     * 获取开票申请业务详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:business:query')")
    @GetMapping(value = "/invoiceStatistics")
    public AjaxResult invoiceStatistics(InvoicingBusinessVo invoicingBusiness)
    {
        return AjaxResult.success(invoicingBusinessService.invoiceStatistics(invoicingBusiness));
    }

    @Log(title = "开票申请对比数据导入校验", businessType = BusinessType.IMPORT)
    @PostMapping("/importDataCheck")
    public AjaxResult importDataCheck(MultipartFile file) throws Exception {
        CheckExcelTemplate.validateExcelTemplate(file, InvoicingBusinessVo.class);
        ExcelUtilCheck<InvoicingBusinessVo> util = new ExcelUtilCheck<InvoicingBusinessVo>(InvoicingBusinessVo.class);
        List<InvoicingBusinessVo> invoicingBusinessVoList = util.importExcel(file.getInputStream());
        if(invoicingBusinessVoList.isEmpty()){
            throw new RuntimeException("检查导入模版是否正确或至少导入一条数据");
        }
        Map<String, List<InvoicingBusinessVo>> map = invoicingBusinessService.importDataCheck(invoicingBusinessVoList);
        return AjaxResult.success(map);
    }

    /**
     * 债转信息导入模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtilCheck<InvoicingBusinessVo> util = new ExcelUtilCheck<InvoicingBusinessVo>(InvoicingBusinessVo.class);
        util.importTemplateExcel(response, "债转信息导入模板");
    }

    /**
     * 开票申请导入数据
     * @param invoicingBusinessVo
     * @return
     */
    @Log(title = "开票申请导入数据", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importData")
    public AjaxResult importData(@RequestBody InvoicingBusinessVo invoicingBusinessVo) throws Exception {
        return AjaxResult.success(invoicingBusinessService.importData(invoicingBusinessVo));
    }

    /**
     * 提交开票申请
     * @param invoicingBusinessVo
     * @return
     */
    @Log(title = "提交开票申请", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/invoicingBusinessBatch")
    public AjaxResult invoicingBusinessBatch(@RequestBody InvoicingBusinessVo invoicingBusinessVo){
        return AjaxResult.success(invoicingBusinessService.invoicingBusinessBatch(invoicingBusinessVo));
    }

    /**
     * 小程序提交开票申请
     * @param invoicingBusinessVo
     * @return
     */
    @Log(title = "小程序提交开票申请", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/miniProgramInvoicing")
    public AjaxResult miniProgramInvoicing(@RequestBody InvoicingBusinessVo invoicingBusinessVo){
        return toAjax(invoicingBusinessService.miniProgramInvoicing(invoicingBusinessVo));
    }
}

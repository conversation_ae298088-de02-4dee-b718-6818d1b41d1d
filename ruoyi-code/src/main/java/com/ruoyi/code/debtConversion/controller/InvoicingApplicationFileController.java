package com.ruoyi.code.debtConversion.controller;

import com.ruoyi.code.debtConversion.domain.InvoicingApplicationFile;
import com.ruoyi.code.debtConversion.domain.until.CheckExcelTemplate;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingApplicationFileVo;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingApplicationImport;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingApplicationVo;
import com.ruoyi.code.debtConversion.service.IInvoicingApplicationFileService;
import com.ruoyi.code.debtConversion.service.IInvoicingApplicationService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 开票申请文件Controller
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@RestController
@RequestMapping("/invoicing/application/file")
public class InvoicingApplicationFileController extends BaseController
{
    @Autowired
    private IInvoicingApplicationFileService invoicingApplicationFileService;
    @Autowired
    private IInvoicingApplicationService invoicingApplicationService;

    /**
     * 查询开票申请文件列表
     */
    //@PreAuthorize("@ss.hasPermi('system:file:list')")
    @GetMapping("/list")
    public TableDataInfo list(InvoicingApplicationFileVo invoicingApplicationFile)
    {
        startPage();
        List<InvoicingApplicationFileVo> list = invoicingApplicationFileService.selectInvoicingApplicationFileList(invoicingApplicationFile);
        return getDataTable(list);
    }

    /**
     * 对比文件导出
     */
    //@PreAuthorize("@ss.hasPermi('system:file:export')")
    @Log(title = "对比文件导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InvoicingApplicationVo invoicingApplicationFile)
    {
        List<InvoicingApplicationImport> list = invoicingApplicationService.selectInvoicingApplicationImport(invoicingApplicationFile);
        ExcelUtil<InvoicingApplicationImport> util = new ExcelUtil<InvoicingApplicationImport>(InvoicingApplicationImport.class);
        util.exportExcel(response, list, "开票申请文件数据");
    }

    /**
     * 获取开票申请文件详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:file:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(invoicingApplicationFileService.selectInvoicingApplicationFileById(id));
    }

    /**
     * 新增开票申请文件
     */
    //@PreAuthorize("@ss.hasPermi('system:file:add')")
    @Log(title = "开票申请文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InvoicingApplicationFile invoicingApplicationFile)
    {
        return toAjax(invoicingApplicationFileService.insertInvoicingApplicationFile(invoicingApplicationFile));
    }

    /**
     * 修改开票申请文件
     */
    //@PreAuthorize("@ss.hasPermi('system:file:edit')")
    @Log(title = "开票申请文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InvoicingApplicationFile invoicingApplicationFile)
    {
        return toAjax(invoicingApplicationFileService.updateInvoicingApplicationFile(invoicingApplicationFile));
    }

    /**
     * 删除开票申请文件
     */
    //@PreAuthorize("@ss.hasPermi('system:file:remove')")
    @Log(title = "开票申请文件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(invoicingApplicationFileService.deleteInvoicingApplicationFileByIds(ids));
    }

    @Log(title = "开票申请对比数据导入校验", businessType = BusinessType.IMPORT)
    @PostMapping("/importDataCheck")
    public AjaxResult importDataCheck(MultipartFile file) throws Exception {
        CheckExcelTemplate.validateExcelTemplate(file, InvoicingApplicationImport.class);
        ExcelUtil<InvoicingApplicationImport> util = new ExcelUtil<InvoicingApplicationImport>(InvoicingApplicationImport.class);
        List<InvoicingApplicationImport> invoicingApplicationImportList = util.importExcel(file.getInputStream());
        if(invoicingApplicationImportList.isEmpty()){
            throw new RuntimeException("检查导入模版是否正确或至少导入一条数据");
        }
        Map<String, List<InvoicingApplicationImport>> map = invoicingApplicationFileService.importDataCheck(invoicingApplicationImportList);
        return AjaxResult.success(map);
    }

    /**
     * 开票申请对比数据导入模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<InvoicingApplicationImport> util = new ExcelUtil<InvoicingApplicationImport>(InvoicingApplicationImport.class);
        util.importTemplateExcel(response, "开票申请对比数据导入模板");
    }

    /**
     * 导入数据
     * @param invoicingApplicationFileVo
     * @return
     */
    @Log(title = "开票申请导入数据", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importData")
    public AjaxResult importData(@RequestBody InvoicingApplicationFileVo invoicingApplicationFileVo) throws Exception {
        return AjaxResult.success(invoicingApplicationFileService.importData(invoicingApplicationFileVo));
    }
}

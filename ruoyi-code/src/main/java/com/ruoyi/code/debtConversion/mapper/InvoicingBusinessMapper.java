package com.ruoyi.code.debtConversion.mapper;

import com.ruoyi.code.debtConversion.domain.InvoicingBusiness;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingBusinessVo;

import java.util.List;

/**
 * 开票申请业务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface InvoicingBusinessMapper
{
    /**
     * 查询开票申请业务
     *
     * @param id 开票申请业务主键
     * @return 开票申请业务
     */
    public InvoicingBusinessVo selectInvoicingBusinessById(Long id);

    /**
     * 查询开票申请业务列表
     *
     * @param invoicingBusiness 开票申请业务
     * @return 开票申请业务集合
     */
    public List<InvoicingBusinessVo> selectInvoicingBusinessList(InvoicingBusinessVo invoicingBusiness);

    /**
     * 新增开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    public int insertInvoicingBusiness(InvoicingBusiness invoicingBusiness);

    /**
     * 新增开票申请业务
     *
     * @param invoicingBusinessList 开票申请业务
     * @return 结果
     */
    public int insertInvoicingBusinessBatch(List<InvoicingBusiness> invoicingBusinessList);
    /**
     * 修改开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    public int updateInvoicingBusiness(InvoicingBusiness invoicingBusiness);
    /**
     * 修改开票申请业务
     *
     * @param invoicingBusiness 开票申请业务
     * @return 结果
     */
    public int uploadInvoice(InvoicingBusinessVo invoicingBusiness);

    /**
     * 删除开票申请业务
     *
     * @param id 开票申请业务主键
     * @return 结果
     */
    public int deleteInvoicingBusinessById(Long id);

    /**
     * 批量删除开票申请业务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInvoicingBusinessByIds(Long[] ids);
}

package com.ruoyi.code.debtConversion.domain.until;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ReadPDFUntil extends PDFTextStripper {

    // 存储所有文本及其位置信息
    private final List<TextWithPosition> textList = new ArrayList<>();
    // 存储提取的发票信息
    private static final Map<String, String> invoiceData = new HashMap<>();

    public ReadPDFUntil() throws IOException {
        super();
    }

    // 内部类用于存储文本及其位置
    private static class TextWithPosition {
        String text;
        float x;
        float y;
        float width;
        float height;

        public TextWithPosition(String text, float x, float y, float width, float height) {
            this.text = text;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }

        @Override
        public String toString() {
            return "Text: " + text + " X: " + x + " Y: " + y;
        }
    }

    @Override
    protected void writeString(String text, List<TextPosition> textPositions) {
        if (textPositions.isEmpty()) {
            return;
        }

        // 获取文本位置信息
        TextPosition firstPosition = textPositions.get(0);
        TextPosition lastPosition = textPositions.get(textPositions.size() - 1);
        float x = firstPosition.getX();
        float y = firstPosition.getY();
        float width = lastPosition.getX() + lastPosition.getWidth() - x;
        float height = firstPosition.getHeight();

        // 存储文本及其位置
        textList.add(new TextWithPosition(text, x, y, width, height));
    }

    /**
     * 解析发票PDF文件并提取关键信息
     *
     * @param filePath 发票PDF文件路径
     * @return 包含发票关键信息的Map
     * @throws IOException 如果文件读取失败
     */
    public static Map<String, String> parseInvoice(String filePath) throws IOException {
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            ReadPDFUntil stripper = new ReadPDFUntil();
            stripper.setSortByPosition(true);
            stripper.getText(document);
            return stripper.extractInvoiceData();
        }
    }

    /**
     * 从收集的文本中提取发票数据
     *
     * @return 包含发票关键信息的Map
     */
    private Map<String, String> extractInvoiceData() {
        // 打印所有文本及其位置，用于调试
//        System.out.println("===== 所有文本及位置信息 =====");
//        for (TextWithPosition text : textList) {
//            System.out.println(text);
//        }

        // 提取购买方名称
        extractBuyerName();

        // 提取价税合计(小写)
        extractTotalAmount();

        return invoiceData;
    }

    /**
     * 提取购买方名称
     */
    private void extractBuyerName() {
        // 直接匹配"名称：周伟"这样的格式
        Pattern pattern = Pattern.compile("名称[：:]\\s*(.+)");
        for (TextWithPosition text : textList) {
            Matcher matcher = pattern.matcher(text.text);
            if (matcher.find()) {
                String buyerName = matcher.group(1).trim();
                // 移除购买方名称中的“(个人)”部分
                buyerName = buyerName.replaceAll("[（(]个人[）)]", "");
                invoiceData.put("buyerName", buyerName);
                return;
            }
        }

        // 如果没有找到明确的「名称」标记，尝试在购买方信息中提取
        pattern = Pattern.compile("购买方[：:]\\s*(.+)");
        for (TextWithPosition text : textList) {
            Matcher matcher = pattern.matcher(text.text);
            if (matcher.find()) {
                // 尝试在附近找到名称信息
                extractNameNearby(text.y);
                return;
            }
        }
    }

    /**
     * 在指定Y坐标附近查找名称信息
     *
     * @param y Y坐标
     */
    private void extractNameNearby(float y) {
        final float NEARBY_THRESHOLD = 50.0f; // 附近的阈值
        Pattern pattern = Pattern.compile("名称[：:]*\\s*(.+)");

        for (TextWithPosition text : textList) {
            if (Math.abs(text.y - y) <= NEARBY_THRESHOLD) {
                Matcher matcher = pattern.matcher(text.text);
                if (matcher.find()) {
                    String buyerName = matcher.group(1).trim();
                    // 移除购买方名称中的“(个人)”部分
                    buyerName = buyerName.replaceAll("[（(]个人[）)]", "");
                    invoiceData.put("buyerName", buyerName);
                    return;
                }
            }
        }
    }

    /**
     * 提取价税合计(小写)
     */
    private void extractTotalAmount() {
        // 直接匹配"（小写）¥169.00"这样的格式
        Pattern pattern = Pattern.compile("[（(]小写[）)]\\s*[¥￥]?([\\d,]+(\\.\\d+)?)");
        for (TextWithPosition text : textList) {
            Matcher matcher = pattern.matcher(text.text);
            if (matcher.find()) {
                invoiceData.put("totalAmount", matcher.group(1).replace(",", ""));
                return;
            }
        }

        // 尝试提取「价税合计」字段
        pattern = Pattern.compile("价税合计[：:]*\\s*[¥￥]?([\\d,]+(\\.\\d+)?)");
        for (TextWithPosition text : textList) {
            Matcher matcher = pattern.matcher(text.text);
            if (matcher.find()) {
                invoiceData.put("totalAmount", matcher.group(1).replace(",", ""));
                return;
            }
        }

        // 尝试匹配「小写」附近的金额格式
        pattern = Pattern.compile("小写[：:]*\\s*[¥￥]?([\\d,]+(\\.\\d+)?)");
        for (TextWithPosition text : textList) {
            Matcher matcher = pattern.matcher(text.text);
            if (matcher.find()) {
                invoiceData.put("totalAmount", matcher.group(1).replace(",", ""));
                return;
            }
        }
    }

    /**
     * 获取提取的信息内容
     *
     * @param filePath PDF文件路径
     * @return 包含购买方名称和价税合计的字符串
     */
    public static String getExtractedContent(String filePath) {
        try {
            // 解析PDF文件
            Map<String, String> data = parseInvoice(filePath);

            // 构建返回内容
            StringBuilder content = new StringBuilder();
            if (data.containsKey("buyerName")) {
                content.append(data.get("buyerName"));
            }
            if (data.containsKey("totalAmount")) {
                content.append("|").append(data.get("totalAmount"));
            }
            return content.toString();
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 主方法用于测试
     */
    public static void main(String[] args) {
//        try {
            String filePath = "doc/uat数据/dzfp熊云辉.pdf";
//            ReadPDFUntil stripper = new ReadPDFUntil();
//            PDDocument document = PDDocument.load(new File(filePath));
//            stripper.setSortByPosition(true);
//            stripper.getText(document);
//            stripper.extractInvoiceData();

            // 直接输出提取的内容
            String content = getExtractedContent(filePath);
            System.out.println("\n===== 提取的发票信息内容 =====");
            System.out.println(content);

//            document.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }
}

package com.ruoyi.code.debtConversion.service.impl;

import com.ruoyi.code.debtConversion.domain.InvoicingApplication;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingApplicationImport;
import com.ruoyi.code.debtConversion.domain.vo.InvoicingApplicationVo;
import com.ruoyi.code.debtConversion.domain.vo.SysCompanyVo;
import com.ruoyi.code.debtConversion.mapper.InvoicingApplicationMapper;
import com.ruoyi.code.debtConversion.service.IInvoicingApplicationService;
import com.ruoyi.code.debtConversion.service.ISysCompanyService;
import com.ruoyi.code.debtConversion.utils.DesensitizationUtils;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 开票申请明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Service
public class InvoicingApplicationServiceImpl implements IInvoicingApplicationService
{
    @Autowired
    private InvoicingApplicationMapper invoicingApplicationMapper;
    @Autowired
    private ISysCompanyService sysCompanyService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询开票申请明细
     *
     * @param id 开票申请明细主键
     * @return 开票申请明细
     */
    @Override
    public InvoicingApplication selectInvoicingApplicationById(Long id)
    {
        return invoicingApplicationMapper.selectInvoicingApplicationById(id);
    }

    /**
     * 查询开票申请明细列表
     *
     * @param invoicingApplication 开票申请明细
     * @return 开票申请明细
     */
    @Override
    public List<InvoicingApplicationVo> selectInvoicingApplicationList(InvoicingApplicationVo invoicingApplication)
    {
        List<InvoicingApplicationVo> invoicingApplicationList = invoicingApplicationMapper.selectInvoicingApplicationList(invoicingApplication);
        List<Long> conpanyIds = invoicingApplicationList.stream()
                .flatMap(dc -> Stream.of(
                        dc.getCustId(),
                        dc.getPartnerId(),
                        dc.getFundId()
                ))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(conpanyIds);
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectSysCompanyList(sysCompanyVo);
        // 将 List<SysCompanyVo> 转换为 Map<Long, String>
        Map<Long, String> sysCompanyMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyShortName));
        invoicingApplicationList.forEach(vo -> {
            vo.setMainBodyName(sysCompanyMap.get(vo.getCustId()));
            vo.setCustName(sysCompanyMap.get(vo.getCustId()));
            vo.setFundName(sysCompanyMap.get(vo.getFundId()));
            vo.setPartnerName(sysCompanyMap.get(vo.getPartnerId()));
        });
        //查询借据是否有历史开票
        List<String> LoanCodeList = invoicingApplicationList.stream().map(InvoicingApplicationVo::getLoanCode).distinct().collect(Collectors.toList());
        InvoicingApplicationVo historyInvoicing = new InvoicingApplicationVo();
        historyInvoicing.setLoanCodeList(LoanCodeList);
        historyInvoicing.setInvoiceStatus("2");
        List<InvoicingApplicationVo> invoicingApplicationVos = invoicingApplicationMapper.selectInvoicingApplicationList(historyInvoicing);
        Map<String, Long> loanCodeToBusinessIdMap = invoicingApplicationVos.stream()
                .filter(vo -> vo.getInvoicingBusinessId() != null) // 过滤掉 null 值
                .collect(Collectors.toMap(
                        InvoicingApplicationVo::getLoanCode,
                        InvoicingApplicationVo::getCustId,
                        (oldValue, newValue) -> oldValue
                ));
        invoicingApplicationList.forEach(vo -> {

            vo.setRealIdCard(vo.getIdCard());
            vo.setRealPhoneNum(vo.getPhoneNum());
            vo.setIdCard(DesensitizationUtils.desensitizeIdCard(vo.getIdCard()));
            vo.setPhoneNum(DesensitizationUtils.desensitizePhone(vo.getPhoneNum()));

            vo.setInvoiceButton(loanCodeToBusinessIdMap.containsKey(vo.getLoanCode()));
            vo.setInvoicingAmountDiff(true);
            vo.setFundNameDiff(true);
            vo.setPartnerNameDiff(true);
            vo.setCustNameDiff(true);
            vo.setSystemFundNameDiff(true);
            vo.setSystemPartnerNameDiff(true);
            vo.setSystemCustNameDiff(true);
            vo.setLoanAmountDiff(true);
            vo.setSame(vo.getInvoicingAmountDiff() && vo.getLoanAmountDiff() && vo.getSystemFundNameDiff() && vo.getSystemPartnerNameDiff() && vo.getSystemCustNameDiff()
                    && vo.getFundNameDiff() && vo.getPartnerNameDiff() && vo.getCustNameDiff());

        });
        return invoicingApplicationList;
    }

    /**
     * 新增开票申请明细
     *
     * @param invoicingApplication 开票申请明细
     * @return 结果
     */
    @Override
    public int insertInvoicingApplication(InvoicingApplication invoicingApplication)
    {
        invoicingApplication.setCreateTime(DateUtils.getNowDate());
        return invoicingApplicationMapper.insertInvoicingApplication(invoicingApplication);
    }

    /**
     * 修改开票申请明细
     *
     * @param invoicingApplication 开票申请明细
     * @return 结果
     */
    @Override
    public int updateInvoicingApplication(InvoicingApplication invoicingApplication)
    {
        invoicingApplication.setUpdateTime(DateUtils.getNowDate());
        return invoicingApplicationMapper.updateInvoicingApplication(invoicingApplication);
    }

    /**
     * 批量删除开票申请明细
     *
     * @param ids 需要删除的开票申请明细主键
     * @return 结果
     */
    @Override
    public int deleteInvoicingApplicationByIds(Long[] ids)
    {
        return invoicingApplicationMapper.deleteInvoicingApplicationByIds(ids);
    }

    /**
     * 删除开票申请明细信息
     *
     * @param id 开票申请明细主键
     * @return 结果
     */
    @Override
    public int deleteInvoicingApplicationById(Long id)
    {
        return invoicingApplicationMapper.deleteInvoicingApplicationById(id);
    }

    /**
     * 批量新增开票申请明细
     *
     * @param invoicingApplication 开票申请明细
     * @return 结果
     */
    @Override
    public int insertInvoicingApplicationBatch(List<InvoicingApplication> invoicingApplication) {
        return invoicingApplicationMapper.insertInvoicingApplicationBatch(invoicingApplication);
    }


    /**
     * 开票申请
     *
     * @param invoicingApplication 开票申请明细
     * @return 结果
     */
    @Override
    public Map<String, Object> implementInvoicingApplication(InvoicingApplicationVo invoicingApplication) {
        Map<String, Object> returnMap = new HashMap<>();
        List<InvoicingApplicationVo> invoicingApplicationVos = selectInvoicingApplicationList(invoicingApplication);

        // 使用LinkedHashMap保持原始顺序
        Map<String, List<InvoicingApplicationVo>> groupMap = new LinkedHashMap<>();

        // 第一次遍历：按关键字段分组
        for (InvoicingApplicationVo ia : invoicingApplicationVos) {
            String key = buildKey(ia);
            groupMap.computeIfAbsent(key, k -> new ArrayList<>()).add(ia);
        }

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        // 获取今天的结束时间（23:59:59）
        LocalDateTime endOfDay = now.toLocalDate().atTime(LocalTime.MAX);
        // 计算剩余秒数
        Duration duration = Duration.between(now, endOfDay);
        long remainingSeconds = duration.getSeconds();
        if (!redisCache.exists("InvoicingApplicationCodeCount")){
            redisCache.setCacheObject("InvoicingApplicationCodeCount",0,remainingSeconds, TimeUnit.SECONDS);
        }
        String format = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        //返回开票列表
        List<InvoicingApplicationVo> returnList = new ArrayList<>();
        // 开票笔数
        AtomicInteger count = new AtomicInteger();

        groupMap.forEach((key, valueList) -> {
            int cacheObject = (int) redisCache.getCacheObject("InvoicingApplicationCodeCount") + 1;
            redisCache.setCacheObject("InvoicingApplicationCodeCount",cacheObject,remainingSeconds, TimeUnit.SECONDS);
            String code = format + cacheObject;

            // 总金额
            BigDecimal amount = valueList.stream()
                    .map(InvoicingApplicationVo::getInvoicingAmount)  // 假设 getInvoicingAmount() 返回 BigDecimal
                    .reduce(BigDecimal.ZERO, BigDecimal::add);  // 初始值 0，然后累加

            valueList.forEach(vo -> {
                vo.setInvoicingApplicationCode(code);
                vo.setAmount(amount);
                returnList.add(vo);
            });
            count.getAndIncrement();
        });

        returnMap.put("rows",returnList);
        returnMap.put("count",count);
        // 总金额
        BigDecimal totalAmount = returnList.stream()
                .map(InvoicingApplicationVo::getInvoicingAmount)  // 假设 getInvoicingAmount() 返回 BigDecimal
                .reduce(BigDecimal.ZERO, BigDecimal::add);  // 初始值 0，然后累加
        returnMap.put("totalAmount",totalAmount);
        return returnMap;
    }

    /**
     * 构建比对键
     */
    private String buildKey(InvoicingApplicationVo dc) {
        return String.join("|",
                dc.getBorrower(),
                dc.getIdCard(),
                dc.getPhoneNum(),
                dc.getReceivingEmail()
        );
    }

    /**
     * 批量新增开票申请明细
     *
     * @param invoicingApplication 开票申请明细
     * @return 结果
     */
    @Override
    public int batchUpdateInvoicingApplicationCode(List<InvoicingApplicationVo> invoicingApplication) {
        return invoicingApplicationMapper.batchUpdateInvoicingApplicationCode(invoicingApplication);
    }

    @Override
    public List<InvoicingApplicationImport> selectInvoicingApplicationImport (InvoicingApplicationVo invoicingApplication){
        List<InvoicingApplicationImport> invoicingApplicationImports = invoicingApplicationMapper.selectInvoicingApplicationImport(invoicingApplication);

        List<Long> conpanyIds = invoicingApplicationImports.stream()
                .flatMap(dc -> Stream.of(
                        dc.getPartnerId(),
                        dc.getFundId()
                ))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(conpanyIds);
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectSysCompanyList(sysCompanyVo);
        // 将 List<SysCompanyVo> 转换为 Map<Long, String>
        Map<Long, String> sysCompanyMap = sysCompanyVos.stream().collect(Collectors.toMap(SysCompanyVo::getId, SysCompanyVo::getCompanyShortName));
        invoicingApplicationImports.forEach(vo -> {
            vo.setFundName(sysCompanyMap.get(vo.getFundId()));
            vo.setPartnerName(sysCompanyMap.get(vo.getPartnerId()));
        });

        return invoicingApplicationImports;
    }
}

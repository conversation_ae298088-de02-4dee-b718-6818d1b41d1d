<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.code.debtConversion.mapper.DimBaseProdInfoMapper">

    <resultMap type="DimBaseProdInfo" id="DimBaseProdInfoResult">
        <result property="id"    column="id"    />
        <result property="productNo"    column="product_no"    />
        <result property="productName"    column="product_name"    />
        <result property="guarantorNo"    column="guarantor_no"    />
        <result property="guarantorName"    column="guarantor_name"    />
        <result property="platformNo"    column="platform_no"    />
        <result property="platformName"    column="platform_name"    />
        <result property="financerNo"    column="financer_no"    />
        <result property="financerName"    column="financer_name"    />
        <result property="projectNo"    column="project_no"    />
        <result property="systemNo"    column="system_no"    />
        <result property="oaPlatformNo"    column="oa_platform_no"    />
        <result property="oaCustNo"    column="oa_cust_no"    />
        <result property="oaPartnerNo"    column="oa_partner_no"    />
        <result property="oaFundNo"    column="oa_fund_no"    />
        <result property="oaItNo"    column="oa_it_no"    />
        <result property="isProjectFinish"    column="is_project_finish"    />
        <result property="projectFinishDate"    column="project_finish_date"    />
        <result property="isProjectCompany"    column="is_project_company"    />
        <result property="isProjectTd"    column="is_project_td"    />
        <result property="isProjectPlan"    column="is_project_plan"    />
        <result property="isProjectPlanUpdate"    column="is_project_plan_update"    />
        <result property="isProjectPlanReset"    column="is_project_plan_reset"    />
        <result property="isProjectRepay1"    column="is_project_repay1"    />
        <result property="isProjectRepay4"    column="is_project_repay4"    />
        <result property="isProjectRepay5"    column="is_project_repay5"    />
        <result property="isProjectRepay7"    column="is_project_repay7"    />
        <result property="isProjectTotalRepay7"    column="is_project_total_repay7"    />
        <result property="isProjectTotalRepay"    column="is_project_total_repay"    />
        <result property="isProjectRepay8"    column="is_project_repay8"    />
        <result property="isProjectRepay7Finish"    column="is_project_repay7_finish"    />
        <result property="isProjectRepay8Normal"    column="is_project_repay8_normal"    />
        <result property="isResultFpd10"    column="is_result_fpd10"    />
        <result property="isResultVintage"    column="is_result_vintage"    />
        <result property="isResultBalanceDistribution"    column="is_result_balance_distribution"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="description"    column="description"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="bizType"    column="biz_type"    />
        <result property="productType"    column="product_type"    />
        <result property="minCreditLimit"    column="min_credit_limit"    />
        <result property="maxCreditLimit"    column="max_credit_limit"    />
        <result property="minInteRate"    column="min_inte_rate"    />
        <result property="maxInteRate"    column="max_inte_rate"    />
        <result property="minCreditSpread"    column="min_credit_spread"    />
        <result property="maxCreditSpread"    column="max_credit_spread"    />
        <result property="grtMethod"    column="grt_method"    />
        <result property="repaymentMethod"    column="repayment_method"    />
        <result property="prepayment"    column="prepayment"    />
        <result property="repaymentData"    column="repayment_data"    />
        <result property="actYearRate"    column="act_year_rate"    />
        <result property="serviceRate"    column="service_rate"    />
        <result property="guaranteeRate"    column="guarantee_rate"    />
        <result property="marginRate"    column="margin_rate"    />
        <result property="compensateRate"    column="compensate_rate"    />
        <result property="periodServiceRate"    column="period_service_rate"    />
        <result property="periodGuaranteeRate"    column="period_guarantee_rate"    />
        <result property="periodMarginRate"    column="period_margin_rate"    />
        <result property="periodCompensateRate"    column="period_compensate_rate"    />
        <result property="ointRate"    column="oint_rate"    />
        <result property="defineRate"    column="define_rate"    />
        <result property="advDefineRate"    column="adv_define_rate"    />
        <result property="compensateDays"    column="compensate_days"    />
        <result property="cpstRuleNo"    column="cpst_rule_no"    />
        <result property="graceDay"    column="grace_day"    />
        <result property="interestFreePeriod"    column="interest_free_period"    />
        <result property="isCpst"    column="is_cpst"    />
        <result property="isRecovery"    column="is_recovery"    />
        <result property="fee1Rate"    column="fee1_rate"    />
        <result property="fee2Rate"    column="fee2_rate"    />
        <result property="fee3Rate"    column="fee3_rate"    />
        <result property="fee4Rate"    column="fee4_rate"    />
        <result property="fee5Rate"    column="fee5_rate"    />
        <result property="fee6Rate"    column="fee6_rate"    />
        <result property="fee7Rate"    column="fee7_rate"    />
        <result property="fee8Rate"    column="fee8_rate"    />
        <result property="dwCreateTime"    column="dw_create_time"    />
        <result property="dwUpdateTime"    column="dw_update_time"    />
        <result property="isEnd"    column="is_end"    />
    </resultMap>

    <sql id="selectDimBaseProdInfoVo">
        select id, product_no, product_name, guarantor_no, guarantor_name, platform_no, platform_name, financer_no, financer_name, project_no, system_no, oa_platform_no, oa_cust_no, oa_partner_no, oa_fund_no, oa_it_no, is_project_finish, project_finish_date, is_project_company, is_project_td, is_project_plan, is_project_plan_update, is_project_plan_reset, is_project_repay1, is_project_repay4, is_project_repay5, is_project_repay7, is_project_total_repay7, is_project_total_repay, is_project_repay8, is_project_repay7_finish, is_project_repay8_normal, is_result_fpd10, is_result_vintage, is_result_balance_distribution, remark, status, check_status, description, create_time, update_time, create_by, update_by, biz_type, product_type, min_credit_limit, max_credit_limit, min_inte_rate, max_inte_rate, min_credit_spread, max_credit_spread, grt_method, repayment_method, prepayment, repayment_data, act_year_rate, service_rate, guarantee_rate, margin_rate, compensate_rate, period_service_rate, period_guarantee_rate, period_margin_rate, period_compensate_rate, oint_rate, define_rate, adv_define_rate, compensate_days, cpst_rule_no, grace_day, interest_free_period, is_cpst, is_recovery, fee1_rate, fee2_rate, fee3_rate, fee4_rate, fee5_rate, fee6_rate, fee7_rate, fee8_rate, dw_create_time, dw_update_time, is_end from dim_base_prod_info
    </sql>

    <select id="selectDimBaseProdInfoList" parameterType="DimBaseProdInfo" resultMap="DimBaseProdInfoResult">
        <include refid="selectDimBaseProdInfoVo"/>
        <where>
            <if test="productNo != null  and productNo != ''"> and product_no = #{productNo}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="guarantorNo != null  and guarantorNo != ''"> and guarantor_no = #{guarantorNo}</if>
            <if test="guarantorName != null  and guarantorName != ''"> and guarantor_name like concat('%', #{guarantorName}, '%')</if>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="platformName != null  and platformName != ''"> and platform_name like concat('%', #{platformName}, '%')</if>
            <if test="financerNo != null  and financerNo != ''"> and financer_no = #{financerNo}</if>
            <if test="financerName != null  and financerName != ''"> and financer_name like concat('%', #{financerName}, '%')</if>
            <if test="projectNo != null  and projectNo != ''"> and project_no = #{projectNo}</if>
            <if test="systemNo != null "> and system_no = #{systemNo}</if>
            <if test="oaPlatformNo != null  and oaPlatformNo != ''"> and oa_platform_no = #{oaPlatformNo}</if>
            <if test="oaCustNo != null  and oaCustNo != ''"> and oa_cust_no = #{oaCustNo}</if>
            <if test="oaPartnerNo != null  and oaPartnerNo != ''"> and oa_partner_no = #{oaPartnerNo}</if>
            <if test="oaFundNo != null  and oaFundNo != ''"> and oa_fund_no = #{oaFundNo}</if>
            <if test="oaItNo != null  and oaItNo != ''"> and oa_it_no = #{oaItNo}</if>
            <if test="isProjectFinish != null  and isProjectFinish != ''"> and is_project_finish = #{isProjectFinish}</if>
            <if test="projectFinishDate != null "> and project_finish_date = #{projectFinishDate}</if>
            <if test="isProjectCompany != null  and isProjectCompany != ''"> and is_project_company = #{isProjectCompany}</if>
            <if test="isProjectTd != null  and isProjectTd != ''"> and is_project_td = #{isProjectTd}</if>
            <if test="isProjectPlan != null  and isProjectPlan != ''"> and is_project_plan = #{isProjectPlan}</if>
            <if test="isProjectPlanUpdate != null  and isProjectPlanUpdate != ''"> and is_project_plan_update = #{isProjectPlanUpdate}</if>
            <if test="isProjectPlanReset != null  and isProjectPlanReset != ''"> and is_project_plan_reset = #{isProjectPlanReset}</if>
            <if test="isProjectRepay1 != null  and isProjectRepay1 != ''"> and is_project_repay1 = #{isProjectRepay1}</if>
            <if test="isProjectRepay4 != null  and isProjectRepay4 != ''"> and is_project_repay4 = #{isProjectRepay4}</if>
            <if test="isProjectRepay5 != null  and isProjectRepay5 != ''"> and is_project_repay5 = #{isProjectRepay5}</if>
            <if test="isProjectRepay7 != null  and isProjectRepay7 != ''"> and is_project_repay7 = #{isProjectRepay7}</if>
            <if test="isProjectTotalRepay7 != null  and isProjectTotalRepay7 != ''"> and is_project_total_repay7 = #{isProjectTotalRepay7}</if>
            <if test="isProjectTotalRepay != null  and isProjectTotalRepay != ''"> and is_project_total_repay = #{isProjectTotalRepay}</if>
            <if test="isProjectRepay8 != null  and isProjectRepay8 != ''"> and is_project_repay8 = #{isProjectRepay8}</if>
            <if test="isProjectRepay7Finish != null  and isProjectRepay7Finish != ''"> and is_project_repay7_finish = #{isProjectRepay7Finish}</if>
            <if test="isProjectRepay8Normal != null  and isProjectRepay8Normal != ''"> and is_project_repay8_normal = #{isProjectRepay8Normal}</if>
            <if test="isResultFpd10 != null  and isResultFpd10 != ''"> and is_result_fpd10 = #{isResultFpd10}</if>
            <if test="isResultVintage != null  and isResultVintage != ''"> and is_result_vintage = #{isResultVintage}</if>
            <if test="isResultBalanceDistribution != null  and isResultBalanceDistribution != ''"> and is_result_balance_distribution = #{isResultBalanceDistribution}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="bizType != null  and bizType != ''"> and biz_type = #{bizType}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="minCreditLimit != null "> and min_credit_limit = #{minCreditLimit}</if>
            <if test="maxCreditLimit != null "> and max_credit_limit = #{maxCreditLimit}</if>
            <if test="minInteRate != null "> and min_inte_rate = #{minInteRate}</if>
            <if test="maxInteRate != null "> and max_inte_rate = #{maxInteRate}</if>
            <if test="minCreditSpread != null "> and min_credit_spread = #{minCreditSpread}</if>
            <if test="maxCreditSpread != null "> and max_credit_spread = #{maxCreditSpread}</if>
            <if test="grtMethod != null  and grtMethod != ''"> and grt_method = #{grtMethod}</if>
            <if test="repaymentMethod != null  and repaymentMethod != ''"> and repayment_method = #{repaymentMethod}</if>
            <if test="prepayment != null  and prepayment != ''"> and prepayment = #{prepayment}</if>
            <if test="repaymentData != null "> and repayment_data = #{repaymentData}</if>
            <if test="actYearRate != null "> and act_year_rate = #{actYearRate}</if>
            <if test="serviceRate != null "> and service_rate = #{serviceRate}</if>
            <if test="guaranteeRate != null "> and guarantee_rate = #{guaranteeRate}</if>
            <if test="marginRate != null "> and margin_rate = #{marginRate}</if>
            <if test="compensateRate != null "> and compensate_rate = #{compensateRate}</if>
            <if test="periodServiceRate != null "> and period_service_rate = #{periodServiceRate}</if>
            <if test="periodGuaranteeRate != null "> and period_guarantee_rate = #{periodGuaranteeRate}</if>
            <if test="periodMarginRate != null "> and period_margin_rate = #{periodMarginRate}</if>
            <if test="periodCompensateRate != null "> and period_compensate_rate = #{periodCompensateRate}</if>
            <if test="ointRate != null "> and oint_rate = #{ointRate}</if>
            <if test="defineRate != null "> and define_rate = #{defineRate}</if>
            <if test="advDefineRate != null "> and adv_define_rate = #{advDefineRate}</if>
            <if test="compensateDays != null "> and compensate_days = #{compensateDays}</if>
            <if test="cpstRuleNo != null  and cpstRuleNo != ''"> and cpst_rule_no = #{cpstRuleNo}</if>
            <if test="graceDay != null "> and grace_day = #{graceDay}</if>
            <if test="interestFreePeriod != null "> and interest_free_period = #{interestFreePeriod}</if>
            <if test="isCpst != null  and isCpst != ''"> and is_cpst = #{isCpst}</if>
            <if test="isRecovery != null  and isRecovery != ''"> and is_recovery = #{isRecovery}</if>
            <if test="fee1Rate != null "> and fee1_rate = #{fee1Rate}</if>
            <if test="fee2Rate != null "> and fee2_rate = #{fee2Rate}</if>
            <if test="fee3Rate != null "> and fee3_rate = #{fee3Rate}</if>
            <if test="fee4Rate != null "> and fee4_rate = #{fee4Rate}</if>
            <if test="fee5Rate != null "> and fee5_rate = #{fee5Rate}</if>
            <if test="fee6Rate != null "> and fee6_rate = #{fee6Rate}</if>
            <if test="fee7Rate != null "> and fee7_rate = #{fee7Rate}</if>
            <if test="fee8Rate != null "> and fee8_rate = #{fee8Rate}</if>
            <if test="dwCreateTime != null "> and dw_create_time = #{dwCreateTime}</if>
            <if test="dwUpdateTime != null "> and dw_update_time = #{dwUpdateTime}</if>
            <if test="isEnd != null  and isEnd != ''"> and is_end = #{isEnd}</if>
        </where>
    </select>

    <select id="selectDimBaseProdInfoById" parameterType="Long" resultMap="DimBaseProdInfoResult">
        <include refid="selectDimBaseProdInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertDimBaseProdInfo" parameterType="DimBaseProdInfo">
        insert into dim_base_prod_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="productNo != null">product_no,</if>
            <if test="productName != null">product_name,</if>
            <if test="guarantorNo != null">guarantor_no,</if>
            <if test="guarantorName != null">guarantor_name,</if>
            <if test="platformNo != null">platform_no,</if>
            <if test="platformName != null">platform_name,</if>
            <if test="financerNo != null">financer_no,</if>
            <if test="financerName != null">financer_name,</if>
            <if test="projectNo != null">project_no,</if>
            <if test="systemNo != null">system_no,</if>
            <if test="oaPlatformNo != null">oa_platform_no,</if>
            <if test="oaCustNo != null">oa_cust_no,</if>
            <if test="oaPartnerNo != null">oa_partner_no,</if>
            <if test="oaFundNo != null">oa_fund_no,</if>
            <if test="oaItNo != null">oa_it_no,</if>
            <if test="isProjectFinish != null">is_project_finish,</if>
            <if test="projectFinishDate != null">project_finish_date,</if>
            <if test="isProjectCompany != null">is_project_company,</if>
            <if test="isProjectTd != null">is_project_td,</if>
            <if test="isProjectPlan != null">is_project_plan,</if>
            <if test="isProjectPlanUpdate != null">is_project_plan_update,</if>
            <if test="isProjectPlanReset != null">is_project_plan_reset,</if>
            <if test="isProjectRepay1 != null">is_project_repay1,</if>
            <if test="isProjectRepay4 != null">is_project_repay4,</if>
            <if test="isProjectRepay5 != null">is_project_repay5,</if>
            <if test="isProjectRepay7 != null">is_project_repay7,</if>
            <if test="isProjectTotalRepay7 != null">is_project_total_repay7,</if>
            <if test="isProjectTotalRepay != null">is_project_total_repay,</if>
            <if test="isProjectRepay8 != null">is_project_repay8,</if>
            <if test="isProjectRepay7Finish != null">is_project_repay7_finish,</if>
            <if test="isProjectRepay8Normal != null">is_project_repay8_normal,</if>
            <if test="isResultFpd10 != null">is_result_fpd10,</if>
            <if test="isResultVintage != null">is_result_vintage,</if>
            <if test="isResultBalanceDistribution != null">is_result_balance_distribution,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="description != null">description,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="bizType != null">biz_type,</if>
            <if test="productType != null">product_type,</if>
            <if test="minCreditLimit != null">min_credit_limit,</if>
            <if test="maxCreditLimit != null">max_credit_limit,</if>
            <if test="minInteRate != null">min_inte_rate,</if>
            <if test="maxInteRate != null">max_inte_rate,</if>
            <if test="minCreditSpread != null">min_credit_spread,</if>
            <if test="maxCreditSpread != null">max_credit_spread,</if>
            <if test="grtMethod != null">grt_method,</if>
            <if test="repaymentMethod != null">repayment_method,</if>
            <if test="prepayment != null">prepayment,</if>
            <if test="repaymentData != null">repayment_data,</if>
            <if test="actYearRate != null">act_year_rate,</if>
            <if test="serviceRate != null">service_rate,</if>
            <if test="guaranteeRate != null">guarantee_rate,</if>
            <if test="marginRate != null">margin_rate,</if>
            <if test="compensateRate != null">compensate_rate,</if>
            <if test="periodServiceRate != null">period_service_rate,</if>
            <if test="periodGuaranteeRate != null">period_guarantee_rate,</if>
            <if test="periodMarginRate != null">period_margin_rate,</if>
            <if test="periodCompensateRate != null">period_compensate_rate,</if>
            <if test="ointRate != null">oint_rate,</if>
            <if test="defineRate != null">define_rate,</if>
            <if test="advDefineRate != null">adv_define_rate,</if>
            <if test="compensateDays != null">compensate_days,</if>
            <if test="cpstRuleNo != null">cpst_rule_no,</if>
            <if test="graceDay != null">grace_day,</if>
            <if test="interestFreePeriod != null">interest_free_period,</if>
            <if test="isCpst != null">is_cpst,</if>
            <if test="isRecovery != null">is_recovery,</if>
            <if test="fee1Rate != null">fee1_rate,</if>
            <if test="fee2Rate != null">fee2_rate,</if>
            <if test="fee3Rate != null">fee3_rate,</if>
            <if test="fee4Rate != null">fee4_rate,</if>
            <if test="fee5Rate != null">fee5_rate,</if>
            <if test="fee6Rate != null">fee6_rate,</if>
            <if test="fee7Rate != null">fee7_rate,</if>
            <if test="fee8Rate != null">fee8_rate,</if>
            <if test="dwCreateTime != null">dw_create_time,</if>
            <if test="dwUpdateTime != null">dw_update_time,</if>
            <if test="isEnd != null">is_end,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="productNo != null">#{productNo},</if>
            <if test="productName != null">#{productName},</if>
            <if test="guarantorNo != null">#{guarantorNo},</if>
            <if test="guarantorName != null">#{guarantorName},</if>
            <if test="platformNo != null">#{platformNo},</if>
            <if test="platformName != null">#{platformName},</if>
            <if test="financerNo != null">#{financerNo},</if>
            <if test="financerName != null">#{financerName},</if>
            <if test="projectNo != null">#{projectNo},</if>
            <if test="systemNo != null">#{systemNo},</if>
            <if test="oaPlatformNo != null">#{oaPlatformNo},</if>
            <if test="oaCustNo != null">#{oaCustNo},</if>
            <if test="oaPartnerNo != null">#{oaPartnerNo},</if>
            <if test="oaFundNo != null">#{oaFundNo},</if>
            <if test="oaItNo != null">#{oaItNo},</if>
            <if test="isProjectFinish != null">#{isProjectFinish},</if>
            <if test="projectFinishDate != null">#{projectFinishDate},</if>
            <if test="isProjectCompany != null">#{isProjectCompany},</if>
            <if test="isProjectTd != null">#{isProjectTd},</if>
            <if test="isProjectPlan != null">#{isProjectPlan},</if>
            <if test="isProjectPlanUpdate != null">#{isProjectPlanUpdate},</if>
            <if test="isProjectPlanReset != null">#{isProjectPlanReset},</if>
            <if test="isProjectRepay1 != null">#{isProjectRepay1},</if>
            <if test="isProjectRepay4 != null">#{isProjectRepay4},</if>
            <if test="isProjectRepay5 != null">#{isProjectRepay5},</if>
            <if test="isProjectRepay7 != null">#{isProjectRepay7},</if>
            <if test="isProjectTotalRepay7 != null">#{isProjectTotalRepay7},</if>
            <if test="isProjectTotalRepay != null">#{isProjectTotalRepay},</if>
            <if test="isProjectRepay8 != null">#{isProjectRepay8},</if>
            <if test="isProjectRepay7Finish != null">#{isProjectRepay7Finish},</if>
            <if test="isProjectRepay8Normal != null">#{isProjectRepay8Normal},</if>
            <if test="isResultFpd10 != null">#{isResultFpd10},</if>
            <if test="isResultVintage != null">#{isResultVintage},</if>
            <if test="isResultBalanceDistribution != null">#{isResultBalanceDistribution},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="description != null">#{description},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="productType != null">#{productType},</if>
            <if test="minCreditLimit != null">#{minCreditLimit},</if>
            <if test="maxCreditLimit != null">#{maxCreditLimit},</if>
            <if test="minInteRate != null">#{minInteRate},</if>
            <if test="maxInteRate != null">#{maxInteRate},</if>
            <if test="minCreditSpread != null">#{minCreditSpread},</if>
            <if test="maxCreditSpread != null">#{maxCreditSpread},</if>
            <if test="grtMethod != null">#{grtMethod},</if>
            <if test="repaymentMethod != null">#{repaymentMethod},</if>
            <if test="prepayment != null">#{prepayment},</if>
            <if test="repaymentData != null">#{repaymentData},</if>
            <if test="actYearRate != null">#{actYearRate},</if>
            <if test="serviceRate != null">#{serviceRate},</if>
            <if test="guaranteeRate != null">#{guaranteeRate},</if>
            <if test="marginRate != null">#{marginRate},</if>
            <if test="compensateRate != null">#{compensateRate},</if>
            <if test="periodServiceRate != null">#{periodServiceRate},</if>
            <if test="periodGuaranteeRate != null">#{periodGuaranteeRate},</if>
            <if test="periodMarginRate != null">#{periodMarginRate},</if>
            <if test="periodCompensateRate != null">#{periodCompensateRate},</if>
            <if test="ointRate != null">#{ointRate},</if>
            <if test="defineRate != null">#{defineRate},</if>
            <if test="advDefineRate != null">#{advDefineRate},</if>
            <if test="compensateDays != null">#{compensateDays},</if>
            <if test="cpstRuleNo != null">#{cpstRuleNo},</if>
            <if test="graceDay != null">#{graceDay},</if>
            <if test="interestFreePeriod != null">#{interestFreePeriod},</if>
            <if test="isCpst != null">#{isCpst},</if>
            <if test="isRecovery != null">#{isRecovery},</if>
            <if test="fee1Rate != null">#{fee1Rate},</if>
            <if test="fee2Rate != null">#{fee2Rate},</if>
            <if test="fee3Rate != null">#{fee3Rate},</if>
            <if test="fee4Rate != null">#{fee4Rate},</if>
            <if test="fee5Rate != null">#{fee5Rate},</if>
            <if test="fee6Rate != null">#{fee6Rate},</if>
            <if test="fee7Rate != null">#{fee7Rate},</if>
            <if test="fee8Rate != null">#{fee8Rate},</if>
            <if test="dwCreateTime != null">#{dwCreateTime},</if>
            <if test="dwUpdateTime != null">#{dwUpdateTime},</if>
            <if test="isEnd != null">#{isEnd},</if>
         </trim>
    </insert>

    <update id="updateDimBaseProdInfo" parameterType="DimBaseProdInfo">
        update dim_base_prod_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="productNo != null">product_no = #{productNo},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="guarantorNo != null">guarantor_no = #{guarantorNo},</if>
            <if test="guarantorName != null">guarantor_name = #{guarantorName},</if>
            <if test="platformNo != null">platform_no = #{platformNo},</if>
            <if test="platformName != null">platform_name = #{platformName},</if>
            <if test="financerNo != null">financer_no = #{financerNo},</if>
            <if test="financerName != null">financer_name = #{financerName},</if>
            <if test="projectNo != null">project_no = #{projectNo},</if>
            <if test="systemNo != null">system_no = #{systemNo},</if>
            <if test="oaPlatformNo != null">oa_platform_no = #{oaPlatformNo},</if>
            <if test="oaCustNo != null">oa_cust_no = #{oaCustNo},</if>
            <if test="oaPartnerNo != null">oa_partner_no = #{oaPartnerNo},</if>
            <if test="oaFundNo != null">oa_fund_no = #{oaFundNo},</if>
            <if test="oaItNo != null">oa_it_no = #{oaItNo},</if>
            <if test="isProjectFinish != null">is_project_finish = #{isProjectFinish},</if>
            <if test="projectFinishDate != null">project_finish_date = #{projectFinishDate},</if>
            <if test="isProjectCompany != null">is_project_company = #{isProjectCompany},</if>
            <if test="isProjectTd != null">is_project_td = #{isProjectTd},</if>
            <if test="isProjectPlan != null">is_project_plan = #{isProjectPlan},</if>
            <if test="isProjectPlanUpdate != null">is_project_plan_update = #{isProjectPlanUpdate},</if>
            <if test="isProjectPlanReset != null">is_project_plan_reset = #{isProjectPlanReset},</if>
            <if test="isProjectRepay1 != null">is_project_repay1 = #{isProjectRepay1},</if>
            <if test="isProjectRepay4 != null">is_project_repay4 = #{isProjectRepay4},</if>
            <if test="isProjectRepay5 != null">is_project_repay5 = #{isProjectRepay5},</if>
            <if test="isProjectRepay7 != null">is_project_repay7 = #{isProjectRepay7},</if>
            <if test="isProjectTotalRepay7 != null">is_project_total_repay7 = #{isProjectTotalRepay7},</if>
            <if test="isProjectTotalRepay != null">is_project_total_repay = #{isProjectTotalRepay},</if>
            <if test="isProjectRepay8 != null">is_project_repay8 = #{isProjectRepay8},</if>
            <if test="isProjectRepay7Finish != null">is_project_repay7_finish = #{isProjectRepay7Finish},</if>
            <if test="isProjectRepay8Normal != null">is_project_repay8_normal = #{isProjectRepay8Normal},</if>
            <if test="isResultFpd10 != null">is_result_fpd10 = #{isResultFpd10},</if>
            <if test="isResultVintage != null">is_result_vintage = #{isResultVintage},</if>
            <if test="isResultBalanceDistribution != null">is_result_balance_distribution = #{isResultBalanceDistribution},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="bizType != null">biz_type = #{bizType},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="minCreditLimit != null">min_credit_limit = #{minCreditLimit},</if>
            <if test="maxCreditLimit != null">max_credit_limit = #{maxCreditLimit},</if>
            <if test="minInteRate != null">min_inte_rate = #{minInteRate},</if>
            <if test="maxInteRate != null">max_inte_rate = #{maxInteRate},</if>
            <if test="minCreditSpread != null">min_credit_spread = #{minCreditSpread},</if>
            <if test="maxCreditSpread != null">max_credit_spread = #{maxCreditSpread},</if>
            <if test="grtMethod != null">grt_method = #{grtMethod},</if>
            <if test="repaymentMethod != null">repayment_method = #{repaymentMethod},</if>
            <if test="prepayment != null">prepayment = #{prepayment},</if>
            <if test="repaymentData != null">repayment_data = #{repaymentData},</if>
            <if test="actYearRate != null">act_year_rate = #{actYearRate},</if>
            <if test="serviceRate != null">service_rate = #{serviceRate},</if>
            <if test="guaranteeRate != null">guarantee_rate = #{guaranteeRate},</if>
            <if test="marginRate != null">margin_rate = #{marginRate},</if>
            <if test="compensateRate != null">compensate_rate = #{compensateRate},</if>
            <if test="periodServiceRate != null">period_service_rate = #{periodServiceRate},</if>
            <if test="periodGuaranteeRate != null">period_guarantee_rate = #{periodGuaranteeRate},</if>
            <if test="periodMarginRate != null">period_margin_rate = #{periodMarginRate},</if>
            <if test="periodCompensateRate != null">period_compensate_rate = #{periodCompensateRate},</if>
            <if test="ointRate != null">oint_rate = #{ointRate},</if>
            <if test="defineRate != null">define_rate = #{defineRate},</if>
            <if test="advDefineRate != null">adv_define_rate = #{advDefineRate},</if>
            <if test="compensateDays != null">compensate_days = #{compensateDays},</if>
            <if test="cpstRuleNo != null">cpst_rule_no = #{cpstRuleNo},</if>
            <if test="graceDay != null">grace_day = #{graceDay},</if>
            <if test="interestFreePeriod != null">interest_free_period = #{interestFreePeriod},</if>
            <if test="isCpst != null">is_cpst = #{isCpst},</if>
            <if test="isRecovery != null">is_recovery = #{isRecovery},</if>
            <if test="fee1Rate != null">fee1_rate = #{fee1Rate},</if>
            <if test="fee2Rate != null">fee2_rate = #{fee2Rate},</if>
            <if test="fee3Rate != null">fee3_rate = #{fee3Rate},</if>
            <if test="fee4Rate != null">fee4_rate = #{fee4Rate},</if>
            <if test="fee5Rate != null">fee5_rate = #{fee5Rate},</if>
            <if test="fee6Rate != null">fee6_rate = #{fee6Rate},</if>
            <if test="fee7Rate != null">fee7_rate = #{fee7Rate},</if>
            <if test="fee8Rate != null">fee8_rate = #{fee8Rate},</if>
            <if test="dwCreateTime != null">dw_create_time = #{dwCreateTime},</if>
            <if test="dwUpdateTime != null">dw_update_time = #{dwUpdateTime},</if>
            <if test="isEnd != null">is_end = #{isEnd},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDimBaseProdInfoById" parameterType="Long">
        delete from dim_base_prod_info where id = #{id}
    </delete>

    <delete id="deleteDimBaseProdInfoByIds" parameterType="String">
        delete from dim_base_prod_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

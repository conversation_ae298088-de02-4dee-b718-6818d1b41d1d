<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.code.debtConversion.mapper.InvoicingBusinessMapper">

    <resultMap type="InvoicingBusinessVo" id="InvoicingBusinessResult">
        <result property="id"    column="id"    />
        <result property="invoicingApplicationCode"    column="invoicing_application_code"    />
        <result property="invoicingApplicationTime"    column="invoicing_application_time"    />
        <result property="channel"    column="channel"    />
        <result property="borrower"    column="borrower"    />
        <result property="phoneNum"    column="phone_num"    />
        <result property="idCard"    column="id_card"    />
        <result property="invoicingAmountTotal"    column="invoicing_amount_total"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="mainBodyId"    column="main_body_id"    />
        <result property="receivingEmail"    column="receiving_email"    />
        <result property="pushTime"    column="push_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="companyShortName" column="company_short_name"    />
        <result property="mainBodyName" column="main_body_name"    />
        <result property="invoicingStatus"    column="invoicing_status"    />
        <result property="invoicingApplicationTimeHHmm"    column="invoicing_application_time"    />
        <result property="weekday"    column="weekday"    />
        <result property="pushWeekday"    column="pushWeekday"    />
        <result property="pushTimeHHmm"    column="push_time"    />
        <result property="reasonFail"    column="reason_fail"    />
<!--        <collection property="invoicingApplicationVoList" column = "invoicing_application_code" select="selectInvoicingApplicationList"/>-->
    </resultMap>

    <resultMap type="InvoicingApplicationVo" id="InvoicingApplicationResult">
        <result property="id"    column="id"    />
        <result property="fileId"    column="file_id"    />
        <result property="loanCode"    column="loan_code"    />
        <result property="borrower"    column="borrower"    />
        <result property="phoneNum"    column="phone_num"    />
        <result property="idCard"    column="id_card"    />
        <result property="invoicingAmount"    column="invoicing_amount"    />
        <result property="fundId"    column="fund_id"    />
        <result property="receivingEmail"    column="receiving_email"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="loanAmount"    column="loan_amount"    />
        <result property="loanTime"    column="loan_time"    />
        <result property="settleTime"    column="settle_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="custId"    column="cust_id"    />
        <result property="invoicingApplicationCode"    column="invoicing_application_code"    />
        <result property="invoicingApplicationTime"    column="invoicing_application_time"    />
    </resultMap>

    <sql id="selectInvoicingBusinessVo">
        select id, invoicing_application_code, invoicing_application_time, channel, borrower, phone_num, id_card, invoicing_amount_total, file_name, file_url, main_body_id, receiving_email, push_time, create_by, create_time, update_by, update_time from dc_invoicing_business
    </sql>

    <select id="selectInvoicingBusinessList" parameterType="InvoicingBusinessVo" resultMap="InvoicingBusinessResult">
        select dib.id, dib.invoicing_application_code, dib.invoicing_application_time, dib.channel, dib.borrower
             , id_card
             , phone_num
             , dib.invoicing_amount_total, dib.file_name, dib.invoicing_status
             , dib.file_url, dib.main_body_id, dib.receiving_email, dib.push_time, dib.create_by, dib.create_time, dib.update_by, dib.update_time
             , DAYOFWEEK(invoicing_application_time) as weekday
        from dc_invoicing_business dib
        <where>
            <if test="completionStatus != null  and completionStatus != ''">
                <if test="completionStatus == 1"> and (file_url is null or file_url = '')</if>
                <if test="completionStatus == 2"> and file_url is not null and file_url != ''</if>
            </if>
            <if test="invoicingApplicationCode != null  and invoicingApplicationCode != ''"> and invoicing_application_code = #{invoicingApplicationCode}</if>
            <if test="invoicingApplicationTime != null "> and invoicing_application_time = #{invoicingApplicationTime}</if>
            <if test="channel != null  and channel != ''"> and channel = #{channel}</if>
            <if test="borrower != null  and borrower != ''"> and borrower like concat('%', #{borrower}, '%')</if>
            <if test="phoneNum != null  and phoneNum != ''"> and AES_DECRYPT(UNHEX(phone_num),'DEBT')  like concat('%', #{phoneNum}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and AES_DECRYPT(UNHEX(id_card),'DEBT') like concat('%', #{idCard}, '%')</if>
            <if test="invoicingAmountTotal != null "> and invoicing_amount_total = #{invoicingAmountTotal}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="mainBodyId != null "> and main_body_id = #{mainBodyId}</if>
            <if test="receivingEmail != null  and receivingEmail != ''"> and receiving_email = #{receivingEmail}</if>
            <if test="pushTime != null "> and push_time = #{pushTime}</if>

            <if test="startInvoicingApplicationTime != null"> and date_format(dib.invoicing_application_time,'%Y-%m-%d') <![CDATA[ >= ]]> date_format(#{startInvoicingApplicationTime},'%Y-%m-%d')</if>
            <if test="endInvoicingApplicationTime != null"> and date_format(dib.invoicing_application_time,'%Y-%m-%d') <![CDATA[ <= ]]> date_format(#{endInvoicingApplicationTime},'%Y-%m-%d')</if>
            <if test="invoicingApplicationCodes != null and invoicingApplicationCodes.size() > 0">
                and dib.invoicing_application_code in
                <foreach collection="invoicingApplicationCodes" item="invoicingApplicationCode" separator="," open="(" close=")">
                    #{invoicingApplicationCode}
                </foreach>
            </if>
            <if test="ids != null and ids.size() > 0">
                and dib.id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="authorityCompanyIds != null and authorityCompanyIds.size() > 0">
                and dib.main_body_id in
                <foreach collection="authorityCompanyIds" item="authorityCompanyId" separator="," open="(" close=")">
                    #{authorityCompanyId}
                </foreach>
            </if>
        </where>
        ORDER BY dib.id DESC
    </select>

    <select id="selectInvoicingBusinessById" parameterType="Long" resultMap="InvoicingBusinessResult">
        select dib.id, dib.invoicing_application_code, dib.invoicing_application_time, dib.channel, dib.borrower
             , CONCAT(LEFT(dib.id_card, 4),'*******',RIGHT(dib.id_card, 4)) AS id_card
             , CONCAT(LEFT(dib.phone_num, 3),'*******',RIGHT(dib.phone_num, 4)) AS phone_num
             , dib.invoicing_amount_total, dib.file_name, dib.invoicing_status
             , dib.file_url, dib.main_body_id, dib.receiving_email, dib.push_time, dib.create_by, dib.create_time, dib.update_by, dib.update_time
             , DAYOFWEEK(invoicing_application_time) as weekday
             , DAYOFWEEK(push_time) as pushWeekday,reason_fail
        from dc_invoicing_business dib
        where dib.id = #{id}
    </select>

    <insert id="insertInvoicingBusiness" parameterType="InvoicingBusiness" useGeneratedKeys="true" keyProperty="id">
        insert into dc_invoicing_business
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoicingApplicationCode != null">invoicing_application_code,</if>
            <if test="invoicingApplicationTime != null">invoicing_application_time,</if>
            <if test="channel != null">channel,</if>
            <if test="borrower != null">borrower,</if>
            <if test="phoneNum != null">phone_num,</if>
            <if test="idCard != null">id_card,</if>
            <if test="invoicingAmountTotal != null">invoicing_amount_total,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="mainBodyId != null">main_body_id,</if>
            <if test="receivingEmail != null">receiving_email,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="reasonDeleteFile != null">reason_delete_file,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoicingApplicationCode != null">#{invoicingApplicationCode},</if>
            <if test="invoicingApplicationTime != null">#{invoicingApplicationTime},</if>
            <if test="channel != null">#{channel},</if>
            <if test="borrower != null">#{borrower},</if>
            <if test="phoneNum != null">#{phoneNum},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="invoicingAmountTotal != null">#{invoicingAmountTotal},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="mainBodyId != null">#{mainBodyId},</if>
            <if test="receivingEmail != null">#{receivingEmail},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="reasonDeleteFile != null">#{reasonDeleteFile},</if>
         </trim>
    </insert>

    <insert id="insertInvoicingBusinessBatch" parameterType="java.util.List">
        insert into dc_invoicing_business
        (invoicing_application_code, invoicing_application_time, channel, borrower, phone_num,
        id_card, invoicing_amount_total, main_body_id, receiving_email, file_url, file_name,create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.invoicingApplicationCode}, #{item.invoicingApplicationTime}, #{item.channel},
            #{item.borrower}, #{item.phoneNum}, #{item.idCard}, #{item.invoicingAmountTotal},
            #{item.mainBodyId}, #{item.receivingEmail}, #{item.fileUrl}, #{item.fileName}, now())
        </foreach>
    </insert>

    <update id="updateInvoicingBusiness" parameterType="InvoicingBusiness">
        update dc_invoicing_business
        <trim prefix="SET" suffixOverrides=",">
            <if test="invoicingApplicationCode != null">invoicing_application_code = #{invoicingApplicationCode},</if>
            <if test="invoicingApplicationTime != null">invoicing_application_time = #{invoicingApplicationTime},</if>
            <if test="channel != null">channel = #{channel},</if>
<!--            <if test="borrower != null">borrower = #{borrower},</if>-->
<!--            <if test="phoneNum != null">phone_num = #{phoneNum},</if>-->
<!--            <if test="idCard != null">id_card = #{idCard},</if>-->
            <if test="invoicingAmountTotal != null">invoicing_amount_total = #{invoicingAmountTotal},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="mainBodyId != null">main_body_id = #{mainBodyId},</if>
            <if test="receivingEmail != null">receiving_email = #{receivingEmail},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="invoicingStatus != null">invoicing_status = #{invoicingStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="reasonFail != null and reasonFail != ''">reason_fail = #{reasonFail},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="uploadInvoice" parameterType="InvoicingBusinessVo">
        update dc_invoicing_business
        set
            <if test="reasonDeleteFile != null">reason_delete_file = #{reasonDeleteFile},</if>
            file_url = #{fileUrl},
            file_name= #{fileName}
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteInvoicingBusinessById" parameterType="Long">
        delete from dc_invoicing_business where id = #{id}
    </delete>

    <delete id="deleteInvoicingBusinessByIds" parameterType="String">
        delete from dc_invoicing_business where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectInvoicingApplicationList" parameterType="InvoicingApplicationVo" resultMap="InvoicingApplicationResult">
        select dia.id, dia.file_id, dia.loan_code, dia.borrower, dia.phone_num, dia.id_card, dia.invoicing_amount
        , dia.fund_id,receiving_email, partner_id, loan_amount, loan_time, settle_time
        , dia.create_by, dia.create_time, dia.update_by, dia.update_time
        , dia.invoicing_application_code, dia.invoicing_application_time
        from dc_invoicing_application dia
        <where>
            <if test="invoicingApplicationCode != null "> and invoicing_application_code = #{invoicingApplicationCode}</if>
        </where>
        ORDER BY dia.id DESC
    </select>

</mapper>

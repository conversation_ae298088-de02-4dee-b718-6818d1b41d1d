<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.code.debtConversion.mapper.InvoicingApplicationMapper">

    <resultMap type="InvoicingApplicationVo" id="InvoicingApplicationResult">
        <result property="id"    column="id"    />
        <result property="fileId"    column="file_id"    />
        <result property="loanCode"    column="loan_code"    />
        <result property="borrower"    column="borrower"    />
        <result property="phoneNum"    column="phone_num"    />
        <result property="idCard"    column="id_card"    />
        <result property="invoicingAmount"    column="invoicing_amount"    />
        <result property="fundId"    column="fund_id"    />
        <result property="receivingEmail"    column="receiving_email"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="loanAmount"    column="loan_amount"    />
        <result property="loanTime"    column="loan_time"    />
        <result property="settleTime"    column="settle_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="invoicingApplicationCode"    column="invoicing_application_code"    />
        <result property="InvoicingApplicationTime"    column="invoicing_application_time"    />
        <result property="custId"    column="cust_id"    />
        <result property="realPhoneNum"    column="real_phone_num"    />
        <result property="realIdCard"    column="real_id_card"    />
    </resultMap>

    <sql id="selectInvoicingApplicationVo">
        select id, file_id, loan_code, borrower, phone_num, id_card, invoicing_amount, fund_id, receiving_email, partner_id, loan_amount, loan_time, settle_time, create_by, create_time, update_by, update_time from dc_invoicing_application
    </sql>

    <select id="selectInvoicingApplicationList" parameterType="InvoicingApplicationVo" resultMap="InvoicingApplicationResult">
        select dia.id, dia.file_id, dia.loan_code, dia.borrower
             , dia.id_card
             , dia.phone_num
             , dia.invoicing_amount
             , dia.fund_id,receiving_email, partner_id, loan_amount, loan_time, settle_time
             , dia.create_by, dia.create_time, dia.update_by, dia.update_time, diaf.main_body_id
             , diaf.main_body_id as cust_id
             , dia.invoicing_application_code, dia.invoicing_application_time
             , dim.invoicing_business_id
        from dc_invoicing_application dia
        left join dc_invoicing_application_file diaf on dia.file_id = diaf.id
        left join dc_invoicing_middle dim on dia.id = dim.correlation_id and dim.correlation_type = '1'
        <where>
            <if test="fileId != null "> and file_id = #{fileId}</if>
            <if test="invoicingBusinessId != null "> and invoicing_business_id = #{invoicingBusinessId}</if>
            <if test="loanCode != null  and loanCode != ''"> and loan_code like concat('%', #{loanCode}, '%')</if>
            <if test="borrower != null  and borrower != ''"> and borrower like concat('%', #{borrower}, '%')</if>
            <if test="phoneNum != null  and phoneNum != ''"> and AES_DECRYPT(UNHEX(phone_num),'DEBT')  like concat('%', #{phoneNum}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and AES_DECRYPT(UNHEX(id_card),'DEBT') like concat('%', #{idCard}, '%')</if>
            <if test="invoicingAmount != null "> and invoicing_amount = #{invoicingAmount}</if>
            <if test="fundId != null "> and fund_id = #{fundId}</if>
            <if test="receivingEmail != null  and receivingEmail != ''"> and receiving_email = #{receivingEmail}</if>
            <if test="partnerId != null "> and partner_id = #{partnerId}</if>
            <if test="loanAmount != null "> and loan_amount = #{loanAmount}</if>
            <if test="loanTime != null "> and loan_time = #{loanTime}</if>
            <if test="settleTime != null "> and settle_time = #{settleTime}</if>
            <if test="invoiceStatus != null and invoiceStatus != ''"> diaf.invoice_status = #{invoiceStatus}</if>
            <if test="invoicingApplicationCode != null "> and dia.invoicing_application_code = #{invoicingApplicationCode}</if>
            <if test="loanCodeList != null and loanCodeList.size() > 0">
                and dia.loan_code in
                <foreach collection="loanCodeList" item="loanCode" separator="," open="(" close=")">
                    #{loanCode}
                </foreach>
            </if>
        </where>
        group by dia.id
        ORDER BY dia.id DESC
    </select>

    <select id="selectInvoicingApplicationImport" parameterType="InvoicingApplicationVo" resultType="InvoicingApplicationImport">
        select dia.id, dia.file_id, dia.loan_code, dia.borrower, dia.phone_num
        , CONCAT(LEFT(dia.id_card, 4),'*******',RIGHT(dia.id_card, 4)) AS id_card
        , dia.invoicing_amount
        , dia.fund_id,receiving_email, partner_id, loan_amount, loan_time, settle_time
        , dia.create_by, dia.create_time, dia.update_by, dia.update_time, diaf.main_body_id
        , diaf.main_body_id as cust_id
        , dia.invoicing_application_code, dia.invoicing_application_time
        , dim.invoicing_business_id
        from dc_invoicing_application dia
        left join dc_invoicing_application_file diaf on dia.file_id = diaf.id
        left join dc_invoicing_middle dim on dia.id = dim.correlation_id and dim.correlation_type = '1'
        <where>
            <if test="fileId != null "> and file_id = #{fileId}</if>
            <if test="invoicingBusinessId != null "> and invoicing_business_id = #{invoicingBusinessId}</if>
            <if test="loanCode != null  and loanCode != ''"> and loan_code like concat('%', #{loanCode}, '%')</if>
            <if test="borrower != null  and borrower != ''"> and borrower like concat('%', #{borrower}, '%')</if>
            <if test="phoneNum != null  and phoneNum != ''"> and phone_num like concat('%', #{phoneNum}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card like concat('%', #{idCard}, '%')</if>
            <if test="invoicingAmount != null "> and invoicing_amount = #{invoicingAmount}</if>
            <if test="fundId != null "> and fund_id = #{fundId}</if>
            <if test="receivingEmail != null  and receivingEmail != ''"> and receiving_email = #{receivingEmail}</if>
            <if test="partnerId != null "> and partner_id = #{partnerId}</if>
            <if test="loanAmount != null "> and loan_amount = #{loanAmount}</if>
            <if test="loanTime != null "> and loan_time = #{loanTime}</if>
            <if test="settleTime != null "> and settle_time = #{settleTime}</if>
            <if test="invoiceStatus != null and invoiceStatus != ''"> diaf.invoice_status = #{invoiceStatus}</if>
            <if test="invoicingApplicationCode != null "> and dia.invoicing_application_code = #{invoicingApplicationCode}</if>
            <if test="loanCodeList != null and loanCodeList.size() > 0">
                and dia.loan_code in
                <foreach collection="loanCodeList" item="loanCode" separator="," open="(" close=")">
                    #{loanCode}
                </foreach>
            </if>
        </where>
        group by dia.id
    </select>

    <select id="selectInvoicingApplicationById" parameterType="Long" resultMap="InvoicingApplicationResult">
        <include refid="selectInvoicingApplicationVo"/>
        where id = #{id}
    </select>

    <insert id="insertInvoicingApplication" parameterType="InvoicingApplication" useGeneratedKeys="true" keyProperty="id">
        insert into dc_invoicing_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">file_id,</if>
            <if test="loanCode != null">loan_code,</if>
            <if test="borrower != null">borrower,</if>
            <if test="phoneNum != null">phone_num,</if>
            <if test="idCard != null">id_card,</if>
            <if test="invoicingAmount != null">invoicing_amount,</if>
            <if test="fundId != null">fund_id,</if>
            <if test="receivingEmail != null">receiving_email,</if>
            <if test="partnerId != null">partner_id,</if>
            <if test="loanAmount != null">loan_amount,</if>
            <if test="loanTime != null">loan_time,</if>
            <if test="settleTime != null">settle_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">#{fileId},</if>
            <if test="loanCode != null">#{loanCode},</if>
            <if test="borrower != null">#{borrower},</if>
            <if test="phoneNum != null">#{phoneNum},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="invoicingAmount != null">#{invoicingAmount},</if>
            <if test="fundId != null">#{fundId},</if>
            <if test="receivingEmail != null">#{receivingEmail},</if>
            <if test="partnerId != null">#{partnerId},</if>
            <if test="loanAmount != null">#{loanAmount},</if>
            <if test="loanTime != null">#{loanTime},</if>
            <if test="settleTime != null">#{settleTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateInvoicingApplication" parameterType="InvoicingApplication">
        update dc_invoicing_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="loanCode != null">loan_code = #{loanCode},</if>
            <if test="borrower != null">borrower = #{borrower},</if>
            <if test="phoneNum != null">phone_num = #{phoneNum},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="invoicingAmount != null">invoicing_amount = #{invoicingAmount},</if>
            <if test="fundId != null">fund_id = #{fundId},</if>
            <if test="receivingEmail != null">receiving_email = #{receivingEmail},</if>
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="loanAmount != null">loan_amount = #{loanAmount},</if>
            <if test="loanTime != null">loan_time = #{loanTime},</if>
            <if test="settleTime != null">settle_time = #{settleTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInvoicingApplicationById" parameterType="Long">
        delete from dc_invoicing_application where id = #{id}
    </delete>

    <delete id="deleteInvoicingApplicationByIds" parameterType="String">
        delete from dc_invoicing_application where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertInvoicingApplicationBatch" parameterType="java.util.List">
        insert into dc_invoicing_application
        (file_id, loan_code, borrower, phone_num, id_card, invoicing_amount,
        fund_id, receiving_email, partner_id, loan_amount, loan_time, settle_time,
        create_by, create_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.fileId}, #{item.loanCode}, #{item.borrower}, #{item.phoneNum},
            #{item.idCard}, #{item.invoicingAmount}, #{item.fundId}, #{item.receivingEmail},
            #{item.partnerId}, #{item.loanAmount}, #{item.loanTime}, #{item.settleTime},
            #{item.createBy}, #{item.createTime}
            )
        </foreach>
    </insert>

    <update id="batchUpdateInvoicingApplicationCode" parameterType="java.util.List">
        UPDATE dc_invoicing_application
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="invoicing_application_code = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.invoicingApplicationCode}
                </foreach>
            </trim>
            <trim prefix="invoicing_application_time = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.invoicingApplicationTime}
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
</mapper>

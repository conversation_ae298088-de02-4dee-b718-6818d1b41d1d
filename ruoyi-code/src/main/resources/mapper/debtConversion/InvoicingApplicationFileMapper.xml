<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.code.debtConversion.mapper.InvoicingApplicationFileMapper">

    <resultMap type="InvoicingApplicationFileVo" id="InvoicingApplicationFileResult">
        <result property="id"    column="id"    />
        <result property="mainBodyId"    column="main_body_id"    />
        <result property="invoicingTheme"    column="invoicing_theme"    />
        <result property="invoiceStatus"    column="invoice_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="mainBodyName"    column="main_body_name"    />
        <result property="createByName"    column="create_name"    />
    </resultMap>

    <sql id="selectInvoicingApplicationFileVo">
        select id, main_body_id, invoicing_theme, create_by, create_time, update_by, update_time from dc_invoicing_application_file
    </sql>

    <select id="selectInvoicingApplicationFileList" parameterType="InvoicingApplicationFileVo" resultMap="InvoicingApplicationFileResult">
        select
            diaf.id, diaf.main_body_id, diaf.invoicing_theme,diaf.invoice_status, diaf.create_by, diaf.create_time, diaf.update_by, diaf.update_time
        from dc_invoicing_application_file diaf
        <where>
            <if test="mainBodyId != null "> and main_body_id = #{mainBodyId}</if>
            <if test="invoicingTheme != null  and invoicingTheme != ''"> and invoicing_theme like concat('%', #{invoicingTheme}, '%')</if>
            <if test="startCreateTime != null"> and date_format(diaf.create_time,'%Y-%m-%d') <![CDATA[ >= ]]> date_format(#{startCreateTime},'%Y-%m-%d')</if>
            <if test="endCreateTime != null"> and date_format(diaf.create_time,'%Y-%m-%d') <![CDATA[ <= ]]> date_format(#{endCreateTime},'%Y-%m-%d')</if>
            <if test="authorityCompanyIds != null and authorityCompanyIds.size() > 0">
                and diaf.main_body_id in
                <foreach collection="authorityCompanyIds" item="authorityCompanyId" separator="," open="(" close=")">
                    #{authorityCompanyId}
                </foreach>
            </if>
        </where>
        ORDER BY diaf.id DESC
    </select>

    <select id="selectInvoicingApplicationFileById" parameterType="Long" resultMap="InvoicingApplicationFileResult">
        <include refid="selectInvoicingApplicationFileVo"/>
        where id = #{id}
    </select>

    <insert id="insertInvoicingApplicationFile" parameterType="InvoicingApplicationFile" useGeneratedKeys="true" keyProperty="id">
        insert into dc_invoicing_application_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mainBodyId != null">main_body_id,</if>
            <if test="invoicingTheme != null">invoicing_theme,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mainBodyId != null">#{mainBodyId},</if>
            <if test="invoicingTheme != null">#{invoicingTheme},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateInvoicingApplicationFile" parameterType="InvoicingApplicationFile">
        update dc_invoicing_application_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="mainBodyId != null">main_body_id = #{mainBodyId},</if>
            <if test="invoicingTheme != null">invoicing_theme = #{invoicingTheme},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInvoicingApplicationFileById" parameterType="Long">
        delete from dc_invoicing_application_file where id = #{id}
    </delete>

    <delete id="deleteInvoicingApplicationFileByIds" parameterType="String">
        delete from dc_invoicing_application_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

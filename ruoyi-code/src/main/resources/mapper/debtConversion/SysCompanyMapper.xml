<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.code.debtConversion.mapper.SysCompanyMapper">

    <resultMap type="SysCompanyVo" id="SysCompanyResult">
        <result property="id"    column="id"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyShortName"    column="company_short_name"    />
        <result property="isInside"    column="is_inside"    />
        <result property="status"    column="status"    />
        <result property="source"    column="source"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="companyCode"    column="company_code"    />
        <result property="companyNo"    column="company_no"    />
        <result property="linkman"    column="linkman"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="postcode"    column="postcode"    />
        <result property="businessAddress"    column="business_address"    />
        <result property="website"    column="website"    />
        <result property="registeredAddress"    column="registered_address"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="projectCount"    column="project_count"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>


    <sql id="selectSysCompanyVo">
        select id, company_name, company_short_name,company_code, is_inside, status, source,check_status,company_code, company_no, linkman, phone, email, postcode, business_address, website, registered_address , create_by, create_time, update_by, update_time,is_delete from sys_company
    </sql>

    <select id="selectSysCompanyList" parameterType="sysCompanyVo" resultMap="SysCompanyResult">
        select
            sc.id, sc.company_name, sc.company_short_name, sc.is_inside, sc.status, sc.source, sc.check_status ,company_code, company_no, linkman, phone, email, postcode, business_address, website, registered_address , sc.create_by, sc.create_time, sc.update_by, sc.update_time,
            sc.is_delete
        from sys_company sc
        <where>
            sc.is_delete = '0'
            <if test="companyName != null  and companyName != ''"> and (company_name like concat('%', #{companyName}, '%') or company_short_name like concat('%', #{companyName}, '%'))</if>
            <if test="isInside != null  and isInside != ''"> and is_inside = #{isInside}</if>
            <if test="status != null  and status != ''"> and sc.status = #{status}</if>
            <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
            <if test="companyIdList != null and companyIdList.size() != 0">
                AND sc.id IN
                <foreach collection="companyIdList" item="companyId" open="(" separator="," close=")">
                    #{companyId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        GROUP BY sc.id
        ORDER BY sc.id desc
    </select>

    <select id="selectSysCompanyById" parameterType="Long" resultMap="SysCompanyResult">
        select
            sc.id, sc.company_name, sc.company_short_name, sc.company_code, sc.is_inside, sc.status, sc.source,sc.check_status ,sc.create_by, sc.create_time, sc.update_by, sc.update_time
        from sys_company sc
        where sc.id = #{id}
        GROUP BY sc.id
    </select>


    <select id="selectSysCompanyByCompanyShortName" parameterType="SysCompany" resultMap="SysCompanyResult">
        <include refid="selectSysCompanyVo"/>
        <where>
            is_delete = '0'
            <if test="companyShortName != null  and companyShortName != ''"> and company_short_name = #{companyShortName}</if>
        </where>
        limit 1
    </select>

    <select id="selectCompanyListByCompanyShortNames" parameterType="java.util.Set" resultType="SysCompanyVo">
        select
            id, company_name, company_short_name
        from  sys_company
        where is_delete = '0' and  company_short_name in
        <foreach collection="collection" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
    </select>

</mapper>

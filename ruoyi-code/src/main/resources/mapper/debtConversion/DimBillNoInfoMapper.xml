<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.code.debtConversion.mapper.DimBillNoInfoMapper">

    <resultMap type="DimBillNoInfo" id="DimBillNoInfoResult">
        <result property="billAppNo"    column="bill_app_no"    />
        <result property="loanMonth"    column="loan_month"    />
        <result property="productNo"    column="product_no"    />
        <result property="loanDate"    column="loan_date"    />
        <result property="dueDate"    column="due_date"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="loanPeriod"    column="loan_period"    />
        <result property="loanTerm"    column="loan_term"    />
        <result property="guarantorNo"    column="guarantor_no"    />
        <result property="idCardType"    column="id_card_type"    />
        <result property="idCard"    column="id_card"    />
        <result property="mobile"    column="mobile"    />
        <result property="name"    column="name"    />
        <result property="farmerFlag"    column="farmer_flag"    />
        <result property="dataSource"    column="data_source"    />
        <result property="provinceId"    column="province_id"    />
        <result property="cityId"    column="city_id"    />
        <result property="dwCreateTime"    column="dw_create_time"    />
        <result property="dwUpdateTime"    column="dw_update_time"    />
    </resultMap>

    <sql id="selectDimBillNoInfoVo">
        select bill_app_no, loan_month, product_no, loan_date, due_date, loan_amt, loan_period, loan_term, guarantor_no, id_card_type, id_card, mobile, name, farmer_flag, data_source, province_id, city_id, dw_create_time, dw_update_time from dim_bill_no_info
    </sql>

    <select id="selectDimBillNoInfoList" parameterType="DimBillNoInfo" resultMap="DimBillNoInfoResult">
        <include refid="selectDimBillNoInfoVo"/>
        <where>
            <if test="loanMonth != null  and loanMonth != ''"> and loan_month = #{loanMonth}</if>
            <if test="productNo != null  and productNo != ''"> and product_no = #{productNo}</if>
            <if test="loanDate != null "> and loan_date = #{loanDate}</if>
            <if test="dueDate != null "> and due_date = #{dueDate}</if>
            <if test="loanAmt != null "> and loan_amt = #{loanAmt}</if>
            <if test="loanPeriod != null "> and loan_period = #{loanPeriod}</if>
            <if test="loanTerm != null "> and loan_term = #{loanTerm}</if>
            <if test="guarantorNo != null  and guarantorNo != ''"> and guarantor_no = #{guarantorNo}</if>
            <if test="idCardType != null  and idCardType != ''"> and id_card_type = #{idCardType}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="farmerFlag != null  and farmerFlag != ''"> and farmer_flag = #{farmerFlag}</if>
            <if test="dataSource != null  and dataSource != ''"> and data_source = #{dataSource}</if>
            <if test="provinceId != null  and provinceId != ''"> and province_id = #{provinceId}</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
            <if test="dwCreateTime != null "> and dw_create_time = #{dwCreateTime}</if>
            <if test="dwUpdateTime != null "> and dw_update_time = #{dwUpdateTime}</if>
        </where>
    </select>

    <select id="selectDimBillNoInfoByBillAppNo" parameterType="String" resultMap="DimBillNoInfoResult">
        <include refid="selectDimBillNoInfoVo"/>
        where bill_app_no = #{billAppNo}
    </select>

    <insert id="insertDimBillNoInfo" parameterType="DimBillNoInfo">
        insert into dim_bill_no_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billAppNo != null">bill_app_no,</if>
            <if test="loanMonth != null">loan_month,</if>
            <if test="productNo != null">product_no,</if>
            <if test="loanDate != null">loan_date,</if>
            <if test="dueDate != null">due_date,</if>
            <if test="loanAmt != null">loan_amt,</if>
            <if test="loanPeriod != null">loan_period,</if>
            <if test="loanTerm != null">loan_term,</if>
            <if test="guarantorNo != null">guarantor_no,</if>
            <if test="idCardType != null">id_card_type,</if>
            <if test="idCard != null">id_card,</if>
            <if test="mobile != null">mobile,</if>
            <if test="name != null">name,</if>
            <if test="farmerFlag != null">farmer_flag,</if>
            <if test="dataSource != null">data_source,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="cityId != null">city_id,</if>
            <if test="dwCreateTime != null">dw_create_time,</if>
            <if test="dwUpdateTime != null">dw_update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billAppNo != null">#{billAppNo},</if>
            <if test="loanMonth != null">#{loanMonth},</if>
            <if test="productNo != null">#{productNo},</if>
            <if test="loanDate != null">#{loanDate},</if>
            <if test="dueDate != null">#{dueDate},</if>
            <if test="loanAmt != null">#{loanAmt},</if>
            <if test="loanPeriod != null">#{loanPeriod},</if>
            <if test="loanTerm != null">#{loanTerm},</if>
            <if test="guarantorNo != null">#{guarantorNo},</if>
            <if test="idCardType != null">#{idCardType},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="name != null">#{name},</if>
            <if test="farmerFlag != null">#{farmerFlag},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="dwCreateTime != null">#{dwCreateTime},</if>
            <if test="dwUpdateTime != null">#{dwUpdateTime},</if>
         </trim>
    </insert>

    <update id="updateDimBillNoInfo" parameterType="DimBillNoInfo">
        update dim_bill_no_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanMonth != null">loan_month = #{loanMonth},</if>
            <if test="productNo != null">product_no = #{productNo},</if>
            <if test="loanDate != null">loan_date = #{loanDate},</if>
            <if test="dueDate != null">due_date = #{dueDate},</if>
            <if test="loanAmt != null">loan_amt = #{loanAmt},</if>
            <if test="loanPeriod != null">loan_period = #{loanPeriod},</if>
            <if test="loanTerm != null">loan_term = #{loanTerm},</if>
            <if test="guarantorNo != null">guarantor_no = #{guarantorNo},</if>
            <if test="idCardType != null">id_card_type = #{idCardType},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="name != null">name = #{name},</if>
            <if test="farmerFlag != null">farmer_flag = #{farmerFlag},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="dwCreateTime != null">dw_create_time = #{dwCreateTime},</if>
            <if test="dwUpdateTime != null">dw_update_time = #{dwUpdateTime},</if>
        </trim>
        where bill_app_no = #{billAppNo}
    </update>

    <delete id="deleteDimBillNoInfoByBillAppNo" parameterType="String">
        delete from dim_bill_no_info where bill_app_no = #{billAppNo}
    </delete>

    <delete id="deleteDimBillNoInfoByBillAppNos" parameterType="String">
        delete from dim_bill_no_info where bill_app_no in
        <foreach item="billAppNo" collection="array" open="(" separator="," close=")">
            #{billAppNo}
        </foreach>
    </delete>

    <select id="miniList" parameterType="DimBillNoInfo" resultType="DimBillNoInfoMini">
        select loan_amt,loan_date,due_date,platform_name,financer_name
        from dim_bill_no_info dbni
        left join dim_base_prod_info dpi on dbni.product_no = dpi.product_no
        <where>
            <if test="loanMonth != null  and loanMonth != ''"> and loan_month = #{loanMonth}</if>
            <if test="productNo != null  and productNo != ''"> and dpi.product_no = #{productNo}</if>
            <if test="loanDate != null "> and loan_date = #{loanDate}</if>
            <if test="dueDate != null "> and due_date = #{dueDate}</if>
            <if test="loanAmt != null "> and loan_amt = #{loanAmt}</if>
            <if test="loanPeriod != null "> and loan_period = #{loanPeriod}</if>
            <if test="loanTerm != null "> and loan_term = #{loanTerm}</if>
            <if test="guarantorNo != null  and guarantorNo != ''"> and dpi.guarantor_no = #{guarantorNo}</if>
            <if test="idCardType != null  and idCardType != ''"> and id_card_type = #{idCardType}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = md5(#{idCard})</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="farmerFlag != null  and farmerFlag != ''"> and farmer_flag = #{farmerFlag}</if>
            <if test="dataSource != null  and dataSource != ''"> and data_source = #{dataSource}</if>
            <if test="provinceId != null  and provinceId != ''"> and province_id = #{provinceId}</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
        </where>
    </select>

    <select id="countByIdCardAndPhone" parameterType="DimBillNoInfo" resultType="int">
        select count(id_card)
        from dm_zz_indiv_info
        <where>
            <if test="name != null  and name != ''"> and name = #{name}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = md5(#{idCard})</if>
            <if test="mobile != null  and mobile != ''"> and mobile = md5(#{mobile})</if>
            <if test="custName != null  and custName != ''"> and guarantor_name = #{custName}</if>
        </where>

    </select>
</mapper>

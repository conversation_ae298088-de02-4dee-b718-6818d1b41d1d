<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.code.debtConversion.mapper.InvoicingMiddleMapper">

    <resultMap type="InvoicingMiddle" id="InvoicingMiddleResult">
        <result property="id"    column="id"    />
        <result property="invoicingBusinessId"    column="invoicing_business_id"    />
        <result property="correlationId"    column="correlation_id"    />
        <result property="correlationType"    column="correlation_type"    />
    </resultMap>

    <sql id="selectInvoicingMiddleVo">
        select id, invoicing_business_id, correlation_id, correlation_type from dc_invoicing_middle
    </sql>

    <select id="selectInvoicingMiddleList" parameterType="InvoicingMiddle" resultMap="InvoicingMiddleResult">
        <include refid="selectInvoicingMiddleVo"/>
        <where>
            <if test="correlationType != null  and correlationType != ''"> and correlation_type = #{correlationType}</if>
        </where>
    </select>

    <select id="selectInvoicingMiddleById" parameterType="Long" resultMap="InvoicingMiddleResult">
        <include refid="selectInvoicingMiddleVo"/>
        where id = #{id}
    </select>

    <insert id="insertInvoicingMiddle" parameterType="InvoicingMiddle">
        insert into dc_invoicing_middle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="invoicingBusinessId != null">invoicing_business_id,</if>
            <if test="correlationId != null">correlation_id,</if>
            <if test="correlationType != null">correlation_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="invoicingBusinessId != null">#{invoicingBusinessId},</if>
            <if test="correlationId != null">#{correlationId},</if>
            <if test="correlationType != null">#{correlationType},</if>
         </trim>
    </insert>

    <!-- 批量新增开票申请业务关联数据 -->
    <insert id="batchInsertInvoicingMiddle" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into dc_invoicing_middle (invoicing_business_id, correlation_id, correlation_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.invoicingBusinessId}, #{item.correlationId}, #{item.correlationType})
        </foreach>
    </insert>

    <update id="updateInvoicingMiddle" parameterType="InvoicingMiddle">
        update dc_invoicing_middle
        <trim prefix="SET" suffixOverrides=",">
            <if test="invoicingBusinessId != null">invoicing_business_id = #{invoicingBusinessId},</if>
            <if test="correlationId != null">correlation_id = #{correlationId},</if>
            <if test="correlationType != null">correlation_type = #{correlationType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInvoicingMiddleById" parameterType="Long">
        delete from dc_invoicing_middle where id = #{id}
    </delete>

    <delete id="deleteInvoicingMiddleByIds" parameterType="String">
        delete from dc_invoicing_middle where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

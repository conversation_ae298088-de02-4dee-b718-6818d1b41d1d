#!/bin/sh
# Linux环境专用启动脚本
# ./ry-linux.sh start 启动 stop 停止 restart 重启 status 状态
AppName=ruoyi-admin.jar

# JVM参数 - 移除了过时的PermSize参数
JVM_OPTS="-Dname=$AppName -Duser.timezone=Asia/Shanghai -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintGCDateStamps -XX:+PrintGCDetails -XX:NewRatio=1 -XX:SurvivorRatio=30 -XX:+UseParallelGC -XX:+UseParallelOldGC"

# Spring配置文件指定为Linux环境
SPRING_PROFILES="-Dspring.profiles.active=linux"

APP_HOME=`pwd`
# 应用日志路径
APP_LOG_PATH=$APP_HOME/logs/$AppName.log
# 系统日志路径
LOG_PATH=$APP_HOME/logs

if [ "$1" = "" ];
then
    echo -e "\033[0;31m 未输入操作名 \033[0m  \033[0;34m {start|stop|restart|status} \033[0m"
    exit 1
fi

if [ "$AppName" = "" ];
then
    echo -e "\033[0;31m 未输入应用名 \033[0m"
    exit 1
fi

function start()
{
    PID=`ps -ef |grep java|grep $AppName|grep -v grep|awk '{print $2}'`

	if [ x"$PID" != x"" ]; then
	    echo "$AppName is running..."
	else
		# 创建必要的目录
		mkdir -p $LOG_PATH
		mkdir -p /home/<USER>/uploadPath
		
		# 设置环境变量并启动应用
		echo "Starting $AppName with Linux profile..."
		nohup env LOG_PATH=$LOG_PATH java $JVM_OPTS $SPRING_PROFILES -jar $AppName > $APP_LOG_PATH 2>&1 &
		echo "Start $AppName success..."
		echo "Log file: $APP_LOG_PATH"
		echo "System logs: $LOG_PATH"
	fi
}

function stop()
{
    echo "Stop $AppName"

	PID=""
	query(){
		PID=`ps -ef |grep java|grep $AppName|grep -v grep|awk '{print $2}'`
	}

	query
	if [ x"$PID" != x"" ]; then
		kill -TERM $PID
		echo "$AppName (pid:$PID) exiting..."
		while [ x"$PID" != x"" ]
		do
			sleep 1
			query
		done
		echo "$AppName exited."
	else
		echo "$AppName already stopped."
	fi
}

function restart()
{
    stop
    sleep 2
    start
}

function status()
{
    PID=`ps -ef |grep java|grep $AppName|grep -v grep|wc -l`
    if [ $PID != 0 ];then
        echo "$AppName is running..."
    else
        echo "$AppName is not running..."
    fi
}

function logs()
{
    echo "Showing application logs..."
    tail -f $APP_LOG_PATH
}

case $1 in
    start)
    start;;
    stop)
    stop;;
    restart)
    restart;;
    status)
    status;;
    logs)
    logs;;
    *)
    echo "Usage: $0 {start|stop|restart|status|logs}"
    exit 1
esac
